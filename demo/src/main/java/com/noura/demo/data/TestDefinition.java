package com.noura.demo.data;

import java.util.Objects;

public class TestDefinition {

		private Integer testId;
		private Integer testGroupId;
		private String testGroupName;
		private String testName;
		private String testUnit;
		private Double minOfRange;
		private Double maxOfRange;

		public TestDefinition() {
		}

		public TestDefinition(Integer testId, Integer testGroupId, String testGroupName,
													String testName, String testUnit, Double minOfRange,
													Double maxOfRange) {

				super();
				setTestId(testId);
				setTestGroupId(testGroupId);
				setTestGroupName(testGroupName);
				setTestName(testName);
				setTestUnit(testUnit);
				setMinOfRange(minOfRange);
				setMaxOfRange(maxOfRange);
		}

		/**
		 * @return the testId
		 */
		public Integer getTestId() {
				return testId;
		}

		/**
		 * @param testId the testId to set
		 */
		public void setTestId(Integer testId) {
				if (null == testId) {
						throw new IllegalArgumentException("The testId input parameter cannot be null.");
				}
				this.testId = testId;
		}

		/**
		 * @return the testGroupId
		 */
		public Integer getTestGroupId() {
				return testGroupId;
		}

		/**
		 * @param testGroupId the testGroupId to set
		 */
		public void setTestGroupId(Integer testGroupId) {
				if (null == testGroupId) {
						throw new IllegalArgumentException("The testGroupId input parameter cannot be null.");
				}
				this.testGroupId = testGroupId;
		}

		/**
		 * @return the testGroupName
		 */
		public String getTestGroupName() {
				return testGroupName;
		}

		/**
		 * @param testName the testName to set
		 */
		public void setTestGroupName(String testGroupName) {
				if (null == testGroupName) {
						throw new IllegalArgumentException("The testGroupName input parameter cannot be null.");
				}
				this.testGroupName = testGroupName;
		}

		/**
		 * @return the testName
		 */
		public String getTestName() {
				return testName;
		}

		/**
		 * @param testName the testName to set
		 */
		public void setTestName(String testName) {
				if (null == testName) {
						throw new IllegalArgumentException("The testName input parameter cannot be null.");
				}
				this.testName = testName;
		}

		/**
		 * @return the testUnit
		 */
		public String getTestUnit() {
				return testUnit;
		}

		/**
		 * @param testUnit the testUnit to set
		 */
		public void setTestUnit(String testUnit) {
				if (null == testUnit) {
						throw new IllegalArgumentException("The testUnit input parameter cannot be null.");
				}
				this.testUnit = testUnit;
		}

		/**
		 * @return the minOfRange
		 */
		public Double getMinOfRange() {
				return minOfRange;
		}

		/**
		 * @param minOfRange the minOfRange to set
		 */
		public void setMinOfRange(Double minOfRange) {
				if ((null == this.maxOfRange) && (null == minOfRange)) {
						throw new IllegalArgumentException("At least 1 of the min and max range input parameters cannot be null.");
				}
				if ((null != this.maxOfRange) && (null != minOfRange) && (minOfRange > this.maxOfRange)) {
						throw new IllegalArgumentException("The minOfRange input parameter must be <= the maxOfRange.");
				}
				this.minOfRange = minOfRange;
		}

		/**
		 * @return the maxOfRange
		 */
		public Double getMaxOfRange() {
				return maxOfRange;
		}

		/**
		 * @param maxOfRange the maxOfRange to set
		 */
		public void setMaxOfRange(Double maxOfRange) {
				if ((null == this.minOfRange) && (null == maxOfRange)) {
						throw new IllegalArgumentException("At least 1 of the min and max range input parameters cannot be null.");
				}
				if ((null != this.minOfRange) && (null != maxOfRange) && (maxOfRange < this.minOfRange)) {
						throw new IllegalArgumentException("The maxOfRange input parameter must be >= the minOfRange.");
				}
				this.maxOfRange = maxOfRange;
		}

		@Override
		public int hashCode() {
				return Objects.hash(maxOfRange, minOfRange, testGroupId, testGroupName,
														testId, testName, testUnit);
		}

		@Override
		public boolean equals(Object obj) {
				if (this == obj) {
						return true;
				}
				if (!(obj instanceof TestDefinition)) {
						return false;
				}
				TestDefinition other = (TestDefinition) obj;
				return Objects.equals(testId, other.testId)
						&& Objects.equals(testGroupId, other.testGroupId) 
						&& Objects.equals(testGroupId, other.testGroupName) 
						&& Objects.equals(testName, other.testName)
						&& Objects.equals(maxOfRange, other.maxOfRange)
						&& Objects.equals(minOfRange, other.minOfRange)
						&& Objects.equals(testUnit, other.testUnit);
		}

}
