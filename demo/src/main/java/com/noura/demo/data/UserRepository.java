package com.noura.demo.data;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Date;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Repository
@Component
public class UserRepository {

		@Autowired
    private JdbcTemplate jdbcTemplate;

		public UserRepository(JdbcTemplate jdbcTemplate) {
				this.jdbcTemplate = jdbcTemplate;
		}

		/**
		 * @param loginId the loginId of the user being searched for
		 * @throws org.springframework.dao.EmptyResultDataAccessException if there is no such user
		 */
		public User findUserByLoginId(String loginId) {

				String findSql = "SELECT * FROM users WHERE login_id = ?;";

				return jdbcTemplate.queryForObject(findSql, new Object[] {loginId}, new UserMapper());

		}

		/**
		 * @param user the user to be saved in the database
		 * @throws org.springframework.dao.EmptyResultDataAccessException if the INSERT or UPDATE
		 * fails and which will then automatically rollback the transaction
		 */
		@Transactional
		public User saveUser(User user) {

				if (user.getUserId() == null) {
						
						Object[] args = { user.getLoginId(), user.getIsActive(),
								user.getPasswd(), user.getFirstName(), user.getMiddleName(),
								user.getLastName(), user.getTitle(), user.getBirthDate(),
								user.getGender(), user.getEmail(), user.getPhone(),
								user.getAddress1(), user.getAddress2(), user.getCity(),
								user.getState(), 1, user.getStartDate(),
								user.getLastLoginDate() };
						
						int[] argTypes = { java.sql.Types.VARCHAR, java.sql.Types.BOOLEAN,
															 java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
															 java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
															 java.sql.Types.VARCHAR, java.sql.Types.DATE,
															 java.sql.Types.CHAR,    java.sql.Types.VARCHAR,
															 java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
															 java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
															 java.sql.Types.VARCHAR, java.sql.Types.INTEGER,
															 java.sql.Types.DATE,    java.sql.Types.DATE };

						// NOTE: The following statement will throw the RuntimeExeption
						// org.springframework.dao.EmptyResultDataAccessException if the
						// INSERT fails. Because it throws a RuntimeException, the
						// transaction will be automagically rolled back.
						Map<String, Object> loginMap
								= jdbcTemplate.queryForMap("INSERT INTO users "
																					 + "(login_id, is_active, passwd, "
																					 + "first_name, middle_name, last_name, title, "
																					 + "birth_date, gender, email, phone, "
																					 + "address_1, address_2, city, state, "
																					 + "role_id, start_date, last_login_date) "
																					 + "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) "
																					 + "ON CONFLICT DO NOTHING RETURNING user_id;",
																					 args, argTypes);
				
						if (null != loginMap) {
								Integer id = (Integer) loginMap.get("user_id");
								System.out.println("user_id = " + id);
								if (id != null) {
										user.setUserId(id);
								}
						}

				} else {

						Integer userId = user.getUserId();
						String loginId = user.getLoginId();
						Integer userVersion = user.getUserVersion();
						if (null == userId || null == loginId || null == userVersion) {
								throw new IllegalArgumentException("The userId, loginId, and userVersion cannot be null.");
						}
						
						Object[] args = { user.getIsActive(), user.getPasswd(), user.getFirstName(),
															user.getMiddleName(), user.getLastName(), user.getTitle(),
															user.getBirthDate(), user.getGender(), user.getEmail(),
															user.getPhone(), user.getAddress1(), user.getAddress2(),
															user.getCity(), user.getState(), 1, user.getStartDate(),
															user.getLastLoginDate(), user.getUserVersion() + 1,
															loginId, userVersion };
						
						int[] argTypes = { java.sql.Types.BOOLEAN, java.sql.Types.VARCHAR,
															 java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
															 java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
															 java.sql.Types.DATE,    java.sql.Types.CHAR,
															 java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
															 java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
															 java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
															 java.sql.Types.INTEGER, java.sql.Types.DATE,
															 java.sql.Types.DATE,    java.sql.Types.INTEGER,
															 java.sql.Types.VARCHAR, java.sql.Types.INTEGER };

						// NOTE: The following statement will throw the RuntimeExeption
						// org.springframework.dao.EmptyResultDataAccessException if the
						// UPDATE fails. Because it throws a RuntimeException, the
						// transaction will be automagically rolled back.
						Map<String, Object> userMap
								= jdbcTemplate.queryForMap("UPDATE users SET "
																					 + "is_active = ?, passwd = ?, "
																					 + "first_name = ?, middle_name = ?, "
																					 + "last_name = ?, title = ?, "
																					 + "birth_date = ?, gender = ?, email = ?, "
																					 + "phone = ?, address_1 = ?, address_2 = ?, "
																					 + "city = ?, state = ?, role_id = ?, "
																					 + "start_date = ?, last_login_date = ?, "
																					 + "user_version = ? "
																					 + "WHERE login_id = ? AND user_version = ? "
																					 + "RETURNING user_version;",
																					 args, argTypes);
						
						if (null != userMap) {
								Integer id = (Integer) userMap.get("user_version");
								System.out.println("user_version = " + id);
								if (id != null) {
										user.setUserVersion(id);
								}
						}

				}

				return user;

		}

}

class UserMapper implements RowMapper<User> {

		@Override
		public User mapRow(ResultSet rs, int rowNum)
				throws SQLException {

				User user = null;
				try {

						user = new User();
						user.setUserId(rs.getInt("user_id"));
						user.setLoginId(rs.getString("login_id"));
						user.setIsActive(rs.getBoolean("is_active"));
						user.setPasswd(rs.getString("passwd"));
						user.setFirstName(rs.getString("first_name"));
						user.setMiddleName(rs.getString("middle_name"));
						user.setLastName(rs.getString("last_name"));
						user.setTitle(rs.getString("title"));
						user.setBirthDate(rs.getDate("birth_date"));
						user.setGender(rs.getString("gender").charAt(0));
						user.setAddress1(rs.getString("address_1"));
						user.setAddress2(rs.getString("address_2"));
						user.setCity(rs.getString("city"));
						user.setState(rs.getString("state"));
						user.setEmail(rs.getString("email"));
						user.setPhone(rs.getString("phone"));
						user.setStartDate(rs.getDate("start_date"));
						Date lastLoginDate = rs.getDate("last_login_date");
						if (null != lastLoginDate) { user.setLastLoginDate(lastLoginDate); }
						user.setUserVersion(rs.getInt("user_version"));

				} catch (IllegalArgumentException iae) {
						iae.printStackTrace();
				}

				return user;
		}
}
