package com.noura.demo.data;

import java.util.Objects;

public class Role {

  private Integer roleId;
  private String roleName;

  public Role() {
  }

  public Role(Integer roleId, String roleName) {
			super();
			setRoleId(roleId);
			setRoleName(roleName);
  }

  /**
   * @return the roleId
   */
  public Integer getRoleId() {
			return roleId;
  }

  /**
   * @param roleId the roleId to set
   */
  public void setRoleId(Integer roleId) {
			if (null == roleId) {
					throw new IllegalArgumentException("The roleId input parameter cannot be null.");
			}
			this.roleId = roleId;
  }

  /**
   * @return the roleName
   */
  public String getRoleName() {
			return roleName;
  }

  /**
   * @param roleName the roleName to set
   */
  public void setRoleName(String roleName) {
			if (null == roleName) {
					throw new IllegalArgumentException("The roleName input parameter cannot be null.");
			}
			this.roleName = roleName;
  }

  @Override
  public int hashCode() {
			return Objects.hash(roleId, roleName);
  }

  @Override
  public boolean equals(Object obj) {
			if (this == obj) {
					return true;
			}
			if (!(obj instanceof Role)) {
					return false;
			}
			Role other = (Role) obj;
			return Objects.equals(roleId, other.roleId) && Objects.equals(roleName, other.roleName);
  }
  
}
