package com.noura.demo.data;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;

import java.util.List;

@Repository
@Component
public class SymptomRepository {

		@Autowired
    private JdbcTemplate jdbcTemplate;

		public SymptomRepository(JdbcTemplate jdbcTemplate) {
				this.jdbcTemplate = jdbcTemplate;
		}

		public List<Symptom> findAllSymptoms() {

				String findSql = "SELECT * "
						+ "FROM symptoms ";

				return jdbcTemplate.query(findSql, new SymptomMapper());

		}
}

class SymptomMapper implements RowMapper<Symptom> {

		@Override
		public Symptom mapRow(ResultSet rs, int rowNum)
				throws SQLException {

				Symptom symptom = null;

				symptom = new Symptom();
				symptom.setSymptomId(rs.getInt("symptom_id"));
				symptom.setSymptomName(rs.getString("symptom_name"));

				return symptom;
		}
}
