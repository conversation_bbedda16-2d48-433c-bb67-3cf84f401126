package com.noura.demo;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.test.context.ActiveProfiles;

import javax.sql.DataSource;

import com.noura.demo.data.UserRepository;
import com.noura.demo.data.SymptomRepository;
import com.noura.demo.data.TestDefinitionRepository;

@Configuration
@ActiveProfiles("test")
public class TestConfig {

		@Value("${spring.datasource.jdbcurl}")
		private String url;
		@Value("${spring.datasource.username}")
		private String username;
		@Value("${spring.datasource.password}")
		private String password;   
		@Value("${spring.datasource.driver-class-name}")
		private String driverClassName;

		@Bean
		@Primary
		public DriverManagerDataSource dataSource() {
				DriverManagerDataSource dataSource = new DriverManagerDataSource();
				dataSource.setDriverClassName(driverClassName);   
				dataSource.setUrl(url);
				dataSource.setUsername(username);
				dataSource.setPassword(password);
				return dataSource;   
		}

		@Bean
		public JdbcTemplate applicationDataConnection() {
				return new JdbcTemplate(dataSource());
    }

		@Bean
		public SymptomRepository applicationSymptomRepository() {
				return new SymptomRepository(applicationDataConnection());
		}
		
		@Bean
		public TestDefinitionRepository applicationTestDefinitionRepository() {
				return new TestDefinitionRepository(applicationDataConnection());
		}
		
		@Bean
		public UserRepository applicationUserRepository() {
				return new UserRepository(applicationDataConnection());
		}

}
