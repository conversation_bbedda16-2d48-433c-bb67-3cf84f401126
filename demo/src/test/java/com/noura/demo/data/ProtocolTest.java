package com.noura.demo.data;

import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;

import com.noura.demo.data.Protocol;

public class ProtocolTest {
		
		private Protocol protocol;

    @Test
    public void setProtocolIdTest() {

				protocol = new Protocol();
				protocol.setProtocolId(0);
				assertTrue(protocol.getProtocolId() == 0);

				try {
						protocol.setProtocolId(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The protocolId input parameter cannot be null.");
				}
		}

    @Test
    public void setProtocolNameTest() {

				protocol = new Protocol();
				protocol.setProtocolName("frob");
				assertTrue(protocol.getProtocolName() == "frob");

				try {
						protocol.setProtocolName(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The protocolName input parameter cannot be null.");
				}
		}

		@Test
		public void protocolConstructorTest() {

				protocol = new Protocol(0, "fudge");
				assertTrue(null != protocol);

		}

}
