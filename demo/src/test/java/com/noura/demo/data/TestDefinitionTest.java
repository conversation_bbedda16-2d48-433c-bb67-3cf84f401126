package com.noura.demo.data;

import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;

import com.noura.demo.data.TestDefinition;

public class TestDefinitionTest {
		
		private TestDefinition testDefinition;

    @Test
    public void setTestDefinitionIdTest() {

				testDefinition = new TestDefinition();
				testDefinition.setTestId(0);
				assertTrue(testDefinition.getTestId() == 0);

				try {
						testDefinition.setTestId(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The testId input parameter cannot be null.");
				}
		}

    @Test
    public void setTestDefinitionGroupIdTest() {

				testDefinition = new TestDefinition();
				testDefinition.setTestGroupId(0);
				assertTrue(testDefinition.getTestGroupId() == 0);

				try {
						testDefinition.setTestGroupId(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The testGroupId input parameter cannot be null.");
				}

				testDefinition.setTestGroupName("Some Test Group");
				assertTrue(testDefinition.getTestGroupName() == "Some Test Group");

				try {
						testDefinition.setTestGroupName(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The testGroupName input parameter cannot be null.");
				}
		}

    @Test
    public void setTestDefinitionUnitTest() {

				testDefinition = new TestDefinition();
				testDefinition.setTestUnit("frob");
				assertTrue(testDefinition.getTestUnit() == "frob");

				try {
						testDefinition.setTestUnit(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The testUnit input parameter cannot be null.");
				}
		}

    @Test
    public void setTestDefinitionRangeTest() {

				Double minOfRange;
				Double maxOfRange;
				testDefinition = new TestDefinition();

				testDefinition.setMinOfRange(0.0);
				assertTrue(testDefinition.getMinOfRange() == 0.0);

				testDefinition.setMaxOfRange(0.0);
				assertTrue(testDefinition.getMaxOfRange() == 0.0);

				try {
						testDefinition.setMinOfRange(null);
						testDefinition.setMaxOfRange(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "At least 1 of the min and max range input parameters cannot be null.");
				}

				try {
						testDefinition.setMaxOfRange(null);
						testDefinition.setMinOfRange(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "At least 1 of the min and max range input parameters cannot be null.");
				}
				
				try {
						testDefinition.setMinOfRange(null);
						testDefinition.setMaxOfRange(0.0);
						testDefinition.setMinOfRange(0.0);
						testDefinition.setMaxOfRange(null);
						testDefinition.setMinOfRange(10.0);
						testDefinition.setMaxOfRange(0.0);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The maxOfRange input parameter must be >= the minOfRange.");
				}

				try {
						testDefinition.setMaxOfRange(null);
						testDefinition.setMinOfRange(0.0);
						testDefinition.setMaxOfRange(0.0);
						testDefinition.setMinOfRange(null);
						testDefinition.setMaxOfRange(0.0);
						testDefinition.setMinOfRange(10.0);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The minOfRange input parameter must be <= the maxOfRange.");
				}

		}

		@Test
		public void testDefinitionConstructorTest() {

				testDefinition = new TestDefinition(0, 0, "frogger", "fnord", "giraffes/zoo", 0.0, 0.0);
				assertTrue(null != testDefinition);

		}

}
