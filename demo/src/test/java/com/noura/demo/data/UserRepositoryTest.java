package com.noura.demo.data;

import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.boot.test.context.SpringBootTest;

import java.sql.Date;

import com.noura.demo.TestConfig;
import com.noura.demo.data.Role;
import com.noura.demo.data.User;
import com.noura.demo.data.UserRepository;

@ContextConfiguration(classes = {TestConfig.class})
@EnableConfigurationProperties
@SpringBootTest
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class UserRepositoryTest { 

		@Autowired
		UserRepository userRepo;

		@Test
		@Order(1)
		public void saveUserTest() {

				try {
						
						User user = new User();
						user.setLoginId("loginId");
						user.setIsActive(true);
						user.setPasswd("passwd");
						user.setFirstName("firstName");
						user.setMiddleName("middleName");
						user.setLastName("lastName");
						user.setTitle("title");
						user.setBirthDate(new Date(System.currentTimeMillis() - 10000));
						user.setGender('M');
						user.setAddress1("address1");
						user.setAddress2("address2");
						user.setCity("city");
						user.setState("ST");
						user.setEmail("email");
						user.setPhone("phone");
						user.setStartDate(new Date(System.currentTimeMillis()));

						user = userRepo.saveUser(user);
						if (user.getUserId() != null) {
								assertTrue(true);
						}
						else {
								assertTrue(false);
						}
				
				} catch (Exception e) {
						System.out.println(e.getMessage());
						e.printStackTrace();
						assertTrue(false);
				}
		}

		@Test
		@Order(3)
		public void UpdateUserTest() {

				User user = null;
				
				try {
						user = userRepo.findUserByLoginId("loginId");
				} catch (org.springframework.dao.EmptyResultDataAccessException erdae) {
						System.out.println(erdae.getMessage());
						assertTrue(false);
				}				

				try {

						user.setIsActive(false);
						user.setPasswd("passwd2");
						user.setFirstName("firstName2");
						user.setMiddleName("middleName2");
						user.setLastName("lastName2");
						user.setTitle("title2");
						user.setBirthDate(new Date(System.currentTimeMillis() - 9000));
						user.setGender('F');
						user.setAddress1("address1-2");
						user.setAddress2("address2-2");
						user.setCity("city2");
						user.setState("OH");
						user.setEmail("email2");
						user.setPhone("phone2");
						user.setStartDate(new Date(System.currentTimeMillis()));
						user.setLastLoginDate(new Date(System.currentTimeMillis()));

						user = userRepo.saveUser(user);
						
				} catch (Exception e) {
						System.out.println(e.getMessage());
						e.printStackTrace();
						assertTrue(false);
				}

				// This update should fail.
				try {
						user.setUserVersion(0);
						user = userRepo.saveUser(user);
				} catch (org.springframework.dao.EmptyResultDataAccessException erdae) {
						System.out.println(erdae.getMessage());
						assertTrue(true);
				}				

		}
		
		@Test
		@Order(2)
		public void findUserByLoginIdTest() {

				User user = null;
				
				try {
						user = userRepo.findUserByLoginId("nonsense");
				} catch (org.springframework.dao.EmptyResultDataAccessException erdae) {
						System.out.println(erdae.getMessage());
						assertTrue(null == user);
				}				

				user = userRepo.findUserByLoginId("loginId");
				assertTrue(null != user);
				assertTrue(user.getLoginId().equals("loginId"));
				assertTrue(user.getIsActive());
				assertTrue(user.getPasswd().equals("passwd"));
				assertTrue(user.getFirstName().equals("firstName"));
				assertTrue(user.getMiddleName().equals("middleName"));
				assertTrue(user.getLastName().equals("lastName"));
				assertTrue(user.getTitle().equals("title"));
				//assertTrue(user.getBirthDate(new Date(System.currentTimeMillis() - 10000));
				assertTrue(user.getGender().equals('M'));
				assertTrue(user.getAddress1().equals("address1"));
				assertTrue(user.getAddress2().equals("address2"));
				assertTrue(user.getCity().equals("city"));
				assertTrue(user.getState().equals("ST"));
				assertTrue(user.getEmail().equals("email"));
				assertTrue(user.getPhone().equals("phone"));
				//assertTrue(user.getStartDate(new Date(System.currentTimeMillis()));

		}
}
