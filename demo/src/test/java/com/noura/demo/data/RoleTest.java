package com.noura.demo.data;

import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;

import com.noura.demo.data.Role;

public class RoleTest {
		
		private Role role;

    @Test
    public void setRoleIdTest() {

				role = new Role();
				role.setRoleId(0);
				assertTrue(role.getRoleId() == 0);

				try {
						role.setRoleId(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The roleId input parameter cannot be null.");
				}
		}

    @Test
    public void setRoleNameTest() {

				role = new Role();
				role.setRoleName("frob");
				assertTrue(role.getRoleName() == "frob");

				try {
						role.setRoleName(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The roleName input parameter cannot be null.");
				}
		}

		@Test
		public void roleConstructorTest() {

				role = new Role(0, "fudge");
				assertTrue(null != role);

		}

}
