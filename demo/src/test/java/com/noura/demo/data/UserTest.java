package com.noura.demo.data;

import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;

import java.sql.Date;
import java.util.ArrayList;

import com.noura.demo.data.User;
import com.noura.demo.data.Role;
import com.noura.demo.data.Ethnicity;
import com.noura.demo.data.PatientSymptom;
import com.noura.demo.data.TestResult;
import com.noura.demo.data.PatientToProtocol;

import java.sql.Date;

public class UserTest {

		private User user;
		private Role role;

    @Test
    public void setUserIdTest() {

				user = new User();
				user.setUserId(0);
				assertTrue(user.getUserId() == 0);

				try {
						user.setUserId(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The userId input parameter cannot be null.");
				}
		}

    @Test
    public void setLoginIdTest() {

				user = new User();
				user.setLoginId("frob");
				assertTrue(user.getLoginId() == "frob");

				try {
						user.setLoginId(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The loginId input parameter cannot be null.");
				}
		}

    @Test
    public void setIsActiveTest() {

				user = new User();
				user.setIsActive(true);
				assertTrue(user.getIsActive() == true);

				try {
						user.setIsActive(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The isActive input parameter cannot be null.");
				}
		}

    @Test
    public void setPasswdTest() {

				user = new User();
				user.setPasswd("grob");
				assertTrue(user.getPasswd() == "grob");
		}
		
    @Test
    public void setFirstNameTest() {

				user = new User();
				user.setFirstName("George");
				assertTrue(user.getFirstName() == "George");

				try {
						user.setFirstName(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The firstName input parameter cannot be null.");
				}
		}

    @Test
    public void setMiddleNameTest() {

				user = new User();
				user.setMiddleName("Fred");
				assertTrue(user.getMiddleName() == "Fred");

		}

    @Test
    public void setLastNameTest() {

				user = new User();
				user.setLastName("Smith");
				assertTrue(user.getLastName() == "Smith");

				try {
						user.setLastName(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The lastName input parameter cannot be null.");
				}
		}

    @Test
    public void setTitleTest() {

				user = new User();
				user.setTitle("Mrs.");
				assertTrue(user.getTitle() == "Mrs.");

		}

    @Test
    public void setBirthDateTest() {

				user = new User();
				Date birthDate = new Date(System.currentTimeMillis());
				
				user.setBirthDate(birthDate);
				assertTrue(user.getBirthDate() == birthDate);

				try {
						user.setBirthDate(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The birthDate input parameter cannot be null.");
				}

				try {
						user.setBirthDate(new Date(System.currentTimeMillis() + 10000));
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The birthDate input parameter cannot be in the future.");
				}

		}

    @Test
    public void setGenderTest() {

				user = new User();
				user.setGender('M');
				assertTrue(user.getGender() == 'M');

				try {
						user.setGender(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The gender input parameter cannot be null.");
				}
		}

    @Test
    public void setAddress1Test() {

				user = new User();
				user.setAddress1("frob");
				assertTrue(user.getAddress1() == "frob");

				try {
						user.setAddress1(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The address1 input parameter cannot be null.");
				}
		}

    @Test
    public void setAddress2Test() {

				user = new User();
				user.setAddress2("clob");
				assertTrue(user.getAddress2() == "clob");

		}

    @Test
    public void setCityTest() {

				user = new User();
				user.setCity("frob");
				assertTrue(user.getCity() == "frob");

				try {
						user.setCity(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The city input parameter cannot be null.");
				}
		}

    @Test
    public void setStateTest() {

				user = new User();
				user.setState("frob");
				assertTrue(user.getState() == "frob");

				try {
						user.setState(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The state input parameter cannot be null.");
				}
		}

    @Test
    public void setEmailTest() {

				user = new User();
				user.setEmail("frob");
				assertTrue(user.getEmail() == "frob");

				try {
						user.setEmail(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The email input parameter cannot be null.");
				}
		}

    @Test
    public void setPhoneTest() {

				user = new User();
				user.setPhone("frob");
				assertTrue(user.getPhone() == "frob");

				try {
						user.setPhone(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The phone input parameter cannot be null.");
				}
		}

    @Test
    public void setStartDateTest() {

				user = new User();
				Date startDate = new Date(System.currentTimeMillis());
				
				user.setStartDate(startDate);
				assertTrue(user.getStartDate() == startDate);

				try {
						user.setStartDate(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The startDate input parameter cannot be null.");
				}
				
				try {
						user.setStartDate(new Date(System.currentTimeMillis() + 10000));
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The startDate input parameter cannot be in the future.");
				}

		}

    @Test
    public void setLastLoginDateTest() {

				user = new User();
				Date startDate = new Date(System.currentTimeMillis());
				user.setStartDate(startDate);

				Date loginDate = new Date(System.currentTimeMillis());
				
				user.setLastLoginDate(loginDate);
				assertTrue(user.getLastLoginDate() == loginDate);

				try {
						loginDate = new Date(System.currentTimeMillis() - 10000);
						user.setLastLoginDate(loginDate);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The lastLoginDate input parameter must be >= the user's startDate.");
				}

		}

    @Test
    public void setRoleTest() {

				user = new User();
				role = new Role();
				
				user.setRole(role);
				assertTrue(user.getRole() == role);

				try {
						user.setRole(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The role input parameter cannot be null.");
				}
		}

    @Test
    public void setEthnicitiesTest() {

				user = new User();

				assertTrue(null != user.getEthnicities());
				user.setEthnicities(null);
				assertTrue(null != user.getEthnicities());

				Ethnicity ethnicity = new Ethnicity();
				ArrayList<Ethnicity> ethnicityList = new ArrayList<Ethnicity>();
				ethnicityList.add(ethnicity);
				user.setEthnicities(ethnicityList);
				assertTrue(user.getEthnicities().contains(ethnicity));

		}

    @Test
    public void setSymptomsTest() {

				user = new User();

				assertTrue(null != user.getSymptoms());
				user.setSymptoms(null);
				assertTrue(null != user.getSymptoms());

				PatientSymptom symptom = new PatientSymptom();
				ArrayList<PatientSymptom> symptomList = new ArrayList<PatientSymptom>();
				symptomList.add(symptom);
				user.setSymptoms(symptomList);
				assertTrue(user.getSymptoms().contains(symptom));

		}

    @Test
    public void setTestResultTest() {

				user = new User();

				assertTrue(null != user.getTestResults());
				user.setTestResults(null);
				assertTrue(null != user.getTestResults());

				TestResult result = new TestResult();
				ArrayList<TestResult> testResultsList = new ArrayList<TestResult>();
				testResultsList.add(result);
				user.setTestResults(testResultsList);
				assertTrue(user.getTestResults().contains(result));

		}

    @Test
    public void setPatientProtocolsTest() {

				user = new User();

				assertTrue(null != user.getPatientProtocols());
				user.setPatientProtocols(null);
				assertTrue(null != user.getPatientProtocols());

				PatientToProtocol protocol = new PatientToProtocol();
				ArrayList<PatientToProtocol> patientProtocolsList = new ArrayList<PatientToProtocol>();
				patientProtocolsList.add(protocol);
				user.setPatientProtocols(patientProtocolsList);
				assertTrue(user.getPatientProtocols().contains(protocol));

		}

    @Test
		public void userConstructorTest() {
				user = new User(0, "loginId", true, "passwd", 
												"firstName", "middleName", "lastName", "title",
												new Date(System.currentTimeMillis()), 'M',
												"address line 1", "address line 2", "city", "state",
												"<EMAIL>", "555-1212", new Date(System.currentTimeMillis()),
												null, 0, new Role(), null, null, null, null);
				assertTrue(null != user);
		}

}
