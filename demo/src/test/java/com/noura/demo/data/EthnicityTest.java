package com.noura.demo.data;

import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;

import com.noura.demo.data.Ethnicity;

public class EthnicityTest {
		
		private Ethnicity ethnicity;

    @Test
    public void setEthnicityIdTest() {

				ethnicity = new Ethnicity();
				ethnicity.setEthnicityId(0);
				assertTrue(ethnicity.getEthnicityId() == 0);

				try {
						ethnicity.setEthnicityId(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The ethnicityId input parameter cannot be null.");
				}
		}

    @Test
    public void setEthnicityNameTest() {

				ethnicity = new Ethnicity();
				ethnicity.setEthnicityName("frob");
				assertTrue(ethnicity.getEthnicityName() == "frob");

				try {
						ethnicity.setEthnicityName(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The ethnicityName input parameter cannot be null.");
				}
		}

		@Test
		public void ethnicityConstructorTest() {

				ethnicity = new Ethnicity(0, "fudge");
				assertTrue(null != ethnicity);

		}

}
