plugins {
	id 'java'
	id 'org.springframework.boot' version '3.5.5'
	id 'io.spring.dependency-management' version '1.1.7'
}

group = 'com.noura'
version = '0.0.1-SNAPSHOT'
description = 'api'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

repositories {
	mavenCentral()
}

ext {
	set('springAiVersion', "1.0.1")
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation 'org.springframework.boot:spring-boot-starter-aop'
	implementation 'org.springframework.ai:spring-ai-advisors-vector-store'
	//implementation 'org.springframework.ai:spring-ai-starter-model-openai'
	//implementation 'org.springframework.ai:spring-ai-starter-vector-store-pgvector'
	implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.2.0'
	implementation 'org.mapstruct:mapstruct:1.5.5.Final'
	compileOnly 'org.projectlombok:lombok'
	developmentOnly 'org.springframework.boot:spring-boot-devtools'
	runtimeOnly 'org.postgresql:postgresql'
	runtimeOnly 'com.h2database:h2'
	annotationProcessor 'org.projectlombok:lombok'
	annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'
	annotationProcessor 'org.projectlombok:lombok-mapstruct-binding:0.2.0'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.springframework.security:spring-security-test'
	testImplementation 'org.testcontainers:junit-jupiter'
	testImplementation 'org.testcontainers:postgresql'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

dependencyManagement {
	imports {
		mavenBom "org.springframework.ai:spring-ai-bom:${springAiVersion}"
	}
}

jar {
	enabled = false
}

tasks.named('test') {
	useJUnitPlatform()
}
