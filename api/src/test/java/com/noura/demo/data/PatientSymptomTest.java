package com.noura.demo.data;

import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;

import com.noura.demo.data.PatientSymptom;
import com.noura.demo.data.Symptom;

import java.sql.Date;

public class PatientSymptomTest {
		
		private PatientSymptom patientSymptom;

    @Test
    public void setUserIdTest() {

				patientSymptom = new PatientSymptom();
				patientSymptom.setUserId(0);
				assertTrue(patientSymptom.getUserId() == 0);

				try {
						patientSymptom.setUserId(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The userId input parameter cannot be null.");
				}
		}

    @Test
    public void setSymptomTest() {

				patientSymptom = new PatientSymptom();
				Symptom symptom = new Symptom();

				patientSymptom.setSymptom(symptom);
				assertTrue(patientSymptom.getSymptom().equals(symptom));

				try {
						patientSymptom.setSymptom(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The symptom input parameter cannot be null.");
				}
		}

    @Test
    public void setDateReportedTest() {

				patientSymptom = new PatientSymptom();
				Date dateReported = new Date(System.currentTimeMillis());
				
				patientSymptom.setDateReported(dateReported);
				assertTrue(patientSymptom.getDateReported() == dateReported);

				try {
						patientSymptom.setDateReported(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The dateReported input parameter cannot be null.");
				}

				try {
						patientSymptom.setDateReported(new Date(System.currentTimeMillis() + 10000));
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The dateReported input parameter cannot be in the future.");
				}

		}

    @Test
    public void setDateEndedTest() {

				patientSymptom = new PatientSymptom();
				Date dateEnded = new Date(System.currentTimeMillis() - 10000);
				Date dateReported = new Date(System.currentTimeMillis());
				
				patientSymptom.setDateReported(dateReported);
				assertTrue(patientSymptom.getDateReported() == dateReported);

				try {
						patientSymptom.setDateEnded(dateEnded);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The dateEnded input parameter must be later than the dateReported.");
				}
		}

    @Test
		public void PatientSymptomConstructorTest() {

				patientSymptom = new PatientSymptom(0, new Symptom(),
																						new Date(System.currentTimeMillis()), null);
				assertTrue(true);
		}

}
