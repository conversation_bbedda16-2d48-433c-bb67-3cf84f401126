package com.noura.demo.data;

import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;

import com.noura.demo.data.Symptom;

public class SymptomTest {
		
		private Symptom symptom;

    @Test
    public void setSymptomIdTest() {

				symptom = new Symptom();
				symptom.setSymptomId(0);
				assertTrue(symptom.getSymptomId() == 0);

				try {
						symptom.setSymptomId(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The symptomId input parameter cannot be null.");
				}
		}

    @Test
    public void setSymptomNameTest() {

				symptom = new Symptom();
				symptom.setSymptomName("frob");
				assertTrue(symptom.getSymptomName() == "frob");

				try {
						symptom.setSymptomName(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The symptomName input parameter cannot be null.");
				}
		}

		@Test
		public void symptomConstructorTest() {

				symptom = new Symptom(0, "fudge");
				assertTrue(null != symptom);

		}

}
