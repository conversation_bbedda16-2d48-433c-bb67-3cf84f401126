package com.noura.demo.data;

import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.boot.test.context.SpringBootTest;

import com.noura.demo.TestConfig;
import com.noura.demo.data.Role;
import com.noura.demo.data.TestDefinition;
import com.noura.demo.data.TestDefinitionRepository;

import java.util.List;

@ContextConfiguration(classes = {TestConfig.class})
@EnableConfigurationProperties
@SpringBootTest
@ActiveProfiles("test")
public class TestDefinitionRepositoryTest { 

		@Autowired
		TestDefinitionRepository testDefRepo;

		@Test
		public void findAllTestsTest() {

				List<TestDefinition> testDefinitions = testDefRepo.findAllTests();
				assertTrue(testDefinitions.size() == 47);

		}

}
