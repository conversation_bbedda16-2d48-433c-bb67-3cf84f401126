package com.noura.demo.data;

import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;

import com.noura.demo.data.TestResult;
import com.noura.demo.data.TestDefinition;

import java.sql.Date;

public class TestResultTest {
		
		private TestResult testResult;

    @Test
    public void setUserIdTest() {

				testResult = new TestResult();
				testResult.setUserId(0);
				assertTrue(testResult.getUserId() == 0);

				try {
						testResult.setUserId(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The userId input parameter cannot be null.");
				}
		}

    @Test
    public void setTestDefinitionTest() {

				testResult = new TestResult();
				TestDefinition testDef = new TestDefinition();
				testResult.setTestDefinition(testDef);
				assertTrue(testResult.getTestDefinition().equals(testDef));

				try {
						testResult.setTestDefinition(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The testDef input parameter cannot be null.");
				}
		}

    @Test
    public void setTestDateTest() {

				testResult = new TestResult();
				Date testDate = new Date(System.currentTimeMillis());
				
				testResult.setTestDate(testDate);
				assertTrue(testResult.getTestDate() == testDate);

				try {
						testResult.setTestDate(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The testDate input parameter cannot be null.");
				}
		}

    @Test
    public void setTestValueTest() {

				testResult = new TestResult();
				testResult.setTestValue(0.0);
				assertTrue(testResult.getTestValue() == 0.0);

				try {
						testResult.setTestValue(null);
						assertTrue(false);
				}
				catch (Exception e) {
						assertTrue(e instanceof IllegalArgumentException);
						assertTrue(e.getMessage() == "The testValue input parameter cannot be null.");
				}
		}

    @Test
		public void TestResultConstructorTest() {

				testResult = new TestResult(0, new TestDefinition(),
																		new Date(System.currentTimeMillis()), 0.0);
				assertTrue(true);

		}
		
}
