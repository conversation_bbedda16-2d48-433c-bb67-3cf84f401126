package com.noura.demo.data;

import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.boot.test.context.SpringBootTest;

import com.noura.demo.TestConfig;
import com.noura.demo.data.Symptom;
import com.noura.demo.data.SymptomRepository;

import java.util.List;

@ContextConfiguration(classes = {TestConfig.class})
@EnableConfigurationProperties
@SpringBootTest
@ActiveProfiles("test")
public class SymptomRepositoryTest { 

		@Autowired
		SymptomRepository symptomRepo;

		@Test
		public void findAllTestsTest() {

				List<Symptom> symptoms = symptomRepo.findAllSymptoms();
				assertTrue(symptoms.size() == 22);

		}

}
