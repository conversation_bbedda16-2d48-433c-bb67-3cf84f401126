package com.noura.api.dto.mapper.users;

import com.noura.api.dto.response.users.UserResponse;
import com.noura.api.model.entity.User;
import org.mapstruct.*;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper(
        componentModel = "spring",
        injectionStrategy = InjectionStrategy.CONSTRUCTOR,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
@Component
public interface UserMapper {

    // RESPONSE MAPPINGS
    // ========================================
    @Named("basic")
    @Mapping(target = "fullName", expression = "java(user.getFirstName() + \" \" + user.getLastName())")
    UserResponse toResponse(User user);

    @Named("detailed")
    @Mapping(target = "fullName", expression = "java(getFullName(user.getFirstName(), user.getLastName()))")
    UserResponse toDetailedResponse(User user);

    // Specify which method to use for the list
    @IterableMapping(qualifiedByName = "basic")
    List<UserResponse> toResponseList(List<User> users);

    // Add a detailed list method if needed
    @IterableMapping(qualifiedByName = "detailed")
    List<UserResponse> toDetailedResponseList(List<User> users);

    // ========================================

    // HELPERS
    // ========================================

    default String getFullName(String firstName, String lastName) {
        if (firstName == null && lastName == null) {
            return null;
        }
        return (firstName != null ? firstName : "") + " " + (lastName != null ? lastName : "");
    }
}