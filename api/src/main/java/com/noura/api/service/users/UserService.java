package com.noura.api.service.users;

import com.noura.api.dto.mapper.users.UserMapper;
import com.noura.api.dto.response.users.UserResponse;
import com.noura.api.exception.custom.users.UserNotFoundException;
import com.noura.api.model.entity.User;
import com.noura.api.repository.users.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;


@Service
@RequiredArgsConstructor
@Slf4j
@Validated
@Transactional
public class UserService {

    private final UserRepository userRepository;
    private final UserMapper userMapper;          // MapStruct generated implementation

    @Transactional(readOnly = true)
    @Cacheable(value = "users", key = "#id")
    public UserResponse getUserById(Long id) {
        log.debug("Fetching user by ID: {}", id);

        User user = userRepository.findById(id)
                .orElseThrow(() -> new UserNotFoundException("User not found with ID: " + id));

        return userMapper.toResponse(user);
    }

    @Transactional(readOnly = true)
    public Page<UserResponse> getAllUsers(Pageable pageable) {
        log.debug("Fetching all users with pagination");

        return userRepository.findAll(pageable)
                .map(userMapper::toResponse);          // MapStruct mapping with method reference
    }
}