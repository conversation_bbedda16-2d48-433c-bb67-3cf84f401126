package com.noura.api.controller.users;

import com.noura.api.dto.response.users.UserResponse;
import com.noura.api.service.users.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
@Slf4j
@Validated
@Tag(name = "User Management", description = "APIs for ")
@CrossOrigin(origins = "http://localhost:5173")
public class UserController {

    private final UserService userService;

//    @PostMapping
//    @Operation(summary = "Create a new user")
//    @ApiResponses(value = {
//            @ApiResponse(responseCode = "201", description = "User created successfully"),
//            @ApiResponse(responseCode = "400", description = "Invalid input data"),
//            @ApiResponse(responseCode = "409", description = "User already exists")
//    })
//    //@PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
//    @PreAuthorize("isAuthenticated()")
//    public ResponseEntity<UserResponse> createUser(@Valid @RequestBody CreateUserRequest request) {
//        log.info("Creating user with email: {}", request.getEmail());
//
//        UserResponse user = userService.createUser(request);
//        return ResponseEntity.status(HttpStatus.CREATED).body(user);  // Direct response
//    }

    @GetMapping("/{id}")
    @Operation(summary = "Get user by ID")
    //@PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<UserResponse> getUserById(@PathVariable Long id) {
        log.info("Fetching user by ID: {}", id);

        UserResponse user = userService.getUserById(id);
        return ResponseEntity.ok(user);  // Direct response
    }

    @GetMapping
    @Operation(summary = "Get all users with pagination")
    // @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Page<UserResponse>> getAllUsers(
            @PageableDefault(size = 20) Pageable pageable) {
        log.info("Fetching all users with pagination: {}", pageable);

        Page<UserResponse> users = userService.getAllUsers(pageable);
        return ResponseEntity.ok(users);  // Direct response
    }

//    @GetMapping("/search")
//    @Operation(summary = "Search users by name")
//    // @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
//    @PreAuthorize("isAuthenticated()")
//    public ResponseEntity<List<UserResponse>> searchUsers(@RequestParam String name) {
//        log.info("Searching users by name: {}", name);
//
//        List<UserResponse> users = userService.searchUsersByName(name);
//        return ResponseEntity.ok(users);  // Direct response
//    }

//    @PutMapping("/{id}")
//    @Operation(summary = "Update user")
//    //@PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
//    @PreAuthorize("isAuthenticated()")
//    public ResponseEntity<UserResponse> updateUser(
//            @PathVariable Long id,
//            @Valid @RequestBody UpdateUserRequest request) {
//        log.info("Updating user with ID: {}", id);
//
//        UserResponse user = userService.updateUser(id, request);
//        return ResponseEntity.ok(user);  // Direct response
//    }

//    @DeleteMapping("/{id}")
//    @ResponseStatus(HttpStatus.NO_CONTENT)  // Use @ResponseStatus for void responses
//    @Operation(summary = "Delete user")
//    // @PreAuthorize("hasRole('ADMIN')")
//    @PreAuthorize("isAuthenticated()")
//    public void deleteUser(@PathVariable Long id) {
//        log.info("Deleting user with ID: {}", id);
//        userService.deleteUser(id);
//        // Return void with @ResponseStatus - Spring handles the response
//    }

//    @GetMapping("/count")
//    @Operation(summary = "Get total user count")
//    //@PreAuthorize("hasRole('ADMIN')")
//    @PreAuthorize("isAuthenticated()")
//    public ResponseEntity<Long> getUserCount() {
//        log.info("Getting user count");
//
//        long count = userService.getUserCount();
//        return ResponseEntity.ok(count);  // Direct primitive response
//    }

//    @PostMapping("/{id}/activate")
//    @Operation(summary = "Activate user")
//    //@PreAuthorize("hasRole('ADMIN')")
//    @PreAuthorize("isAuthenticated()")
//    public ResponseEntity<UserResponse> activateUser(@PathVariable Long id) {
//        log.info("Activating user with ID: {}", id);
//
//        UpdateUserRequest request = UpdateUserRequest.builder()
//                .status(UserStatus.ACTIVE)
//                .build();
//
//        UserResponse user = userService.updateUser(id, request);
//        return ResponseEntity.ok(user);
//    }
}