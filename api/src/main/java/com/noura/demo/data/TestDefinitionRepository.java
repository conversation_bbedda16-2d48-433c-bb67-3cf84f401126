package com.noura.demo.data;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;

import java.util.List;

@Repository
@Component
public class TestDefinitionRepository {

		@Autowired
    private JdbcTemplate jdbcTemplate;

		public TestDefinitionRepository(JdbcTemplate jdbcTemplate) {
				this.jdbcTemplate = jdbcTemplate;
		}

		public List<TestDefinition> findAllTests() {

				String findSql = "SELECT tdef.*, tgrp.test_group_name "
						+ "FROM test_definitions tdef "
						+ "INNER JOIN test_groups tgrp "
						+ "ON (tgrp.test_group_id = tdef.test_group_id) ";

				return jdbcTemplate.query(findSql, new TestDefinitionMapper());

		}
}

class TestDefinitionMapper implements RowMapper<TestDefinition> {

		@Override
		public TestDefinition mapRow(ResultSet rs, int rowNum)
				throws SQLException {

				TestDefinition testDef = null;
				try {

						testDef = new TestDefinition();
						testDef.setTestId(rs.getInt("test_id"));
						testDef.setTestGroupId(rs.getInt("test_group_id"));
						testDef.setTestName(rs.getString("test_name"));
						testDef.setTestUnit(rs.getString("test_unit"));
						Double minVal = rs.getDouble("min_of_range");
						if (!rs.wasNull()) { testDef.setMinOfRange(minVal); }
						Double maxVal = rs.getDouble("max_of_range");
						if (!rs.wasNull()) { testDef.setMaxOfRange(maxVal); }

				} catch (IllegalArgumentException iae) {
						iae.printStackTrace();
				}

				return testDef;
		}
}
