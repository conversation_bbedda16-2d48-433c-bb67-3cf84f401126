package com.noura.demo.data;

import java.util.Objects;

public class Protocol {

		private Integer protocolId;
		private String protocolName;

		public Protocol() {
		}

		public Protocol(Integer protocolId, String protocolName) {
				super();
				setProtocolId(protocolId);
				setProtocolName(protocolName);
	}

		/**
		 * @return the protocolId
		 */
		public Integer getProtocolId() {
				return protocolId;
		}

		/**
		 * @param protocolId the protocolId to set
		 */
		public void setProtocolId(Integer protocolId) {
				if (null == protocolId) {
						throw new IllegalArgumentException("The protocolId input parameter cannot be null.");
				}
				this.protocolId = protocolId;
		}
		
		/**
		 * @return the protocolName
		 */
		public String getProtocolName() {
				return protocolName;
		}
		
		/**
		 * @param protocolName the protocolName to set
		 */
		public void setProtocolName(String protocolName) {
				if (null == protocolName) {
						throw new IllegalArgumentException("The protocolName input parameter cannot be null.");
				}
				this.protocolName = protocolName;
		}
		
		@Override
		public int hashCode() {
				return Objects.hash(protocolId, protocolName);
		}
		
		@Override
		public boolean equals(Object obj) {
				if (this == obj) {
						return true;
				}
				if (!(obj instanceof Protocol)) {
						return false;
				}
				Protocol other = (Protocol) obj;
				return Objects.equals(protocolId, other.protocolId) && Objects.equals(protocolName, other.protocolName);
		}

}
