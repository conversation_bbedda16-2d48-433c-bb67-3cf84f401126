package com.noura.demo.data;

import java.sql.Date;
import java.util.Objects;

public class PatientToProtocol {

	private Integer userId;
	private Protocol protocol;
	private Date startDate;
	private Date endDate;

	public PatientToProtocol() {
	}

	public PatientToProtocol(Integer userId, Protocol protocol, Date startDate, Date endDate) {
		super();
		this.userId = userId;
		this.protocol = protocol;
		this.startDate = startDate;
		this.endDate = endDate;
	}

	/**
	 * @return the userId
	 */
	public Integer getUserId() {
		return userId;
	}

	/**
	 * @param userId the userId to set
	 */
	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	/**
	 * @return the protocol
	 */
	public Protocol getProtocol() {
		return protocol;
	}

	/**
	 * @param protocolId the protocol to set
	 */
	public void setProtocolId(Protocol protocol) {
		this.protocol = protocol;
	}

	/**
	 * @return the startDate
	 */
	public Date getStartDate() {
		return startDate;
	}

	/**
	 * @param startDate the startDate to set
	 */
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	/**
	 * @return the endDate
	 */
	public Date getEndDate() {
		return endDate;
	}

	/**
	 * @param endDate the endDate to set
	 */
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	@Override
	public int hashCode() {
		return Objects.hash(endDate, userId, protocol, startDate);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj) {
			return true;
		}
		if (!(obj instanceof PatientToProtocol)) {
			return false;
		}
		PatientToProtocol other = (PatientToProtocol) obj;
		return Objects.equals(endDate, other.endDate) && Objects.equals(userId, other.userId)
				&& Objects.equals(protocol, other.protocol) && Objects.equals(startDate, other.startDate);
	}

}
