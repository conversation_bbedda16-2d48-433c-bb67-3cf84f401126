package com.noura.demo.data;

import java.sql.Date;
import java.util.Objects;

public class PatientSymptom {

		private Integer userId;
		private Symptom symptom;
		private Date dateReported;
		private Date dateEnded;

		public PatientSymptom() {
		}

		public PatientSymptom(Integer userId, Symptom symptom, Date dateReported,
													Date dateEnded) {
				super();
				setUserId(userId);
				setSymptom(symptom);
				setDateReported(dateReported);
				setDateEnded(dateEnded);
		}

		/**
		 * @return the userId
		 */
		public Integer getUserId() {
				return userId;
		}

		/**
		 * @param userId the userId to set
		 */
		public void setUserId(Integer userId) {
				if (null == userId) { 
						throw new IllegalArgumentException("The userId input parameter cannot be null."); 
				}
				this.userId = userId;
		}

		/**
		 * @return the symptom
		 */
		public Symptom getSymptom() {
				return symptom;
		}

		/**
		 * @param symptom the symptom to set
		 */
		public void setSymptom(Symptom symptom) {
				if (null == symptom) { 
						throw new IllegalArgumentException("The symptom input parameter cannot be null."); 
				}
				this.symptom = symptom;
		}

		/**
		 * @return the dateReported
		 */
		public Date getDateReported() {
				return dateReported;
		}

		/**
		 * @param dateReported the dateReported to set
		 */
		public void setDateReported(Date dateReported) {
				if (null == dateReported) { 
						throw new IllegalArgumentException("The dateReported input parameter cannot be null."); 
				}
				
				Date now = new Date(System.currentTimeMillis());
				if (now.before(dateReported)) {
						throw new IllegalArgumentException("The dateReported input parameter cannot be in the future."); 
				}
		
				this.dateReported = dateReported;
		}

		/**
		 * @return the dateEnded
		 */
		public Date getDateEnded() {
				return dateEnded;
		}

		/**
		 * @param dateEnded the dateEnded to set
		 */
		public void setDateEnded(Date dateEnded) {
				if ((this.dateReported != null) && (dateEnded != null)
						&& dateEnded.before(dateReported)) { 
						throw new IllegalArgumentException("The dateEnded input parameter must be later than the dateReported.");
				}
				this.dateEnded = dateEnded;
		}

		@Override
		public int hashCode() {
				return Objects.hash(dateReported, userId, symptom);
		}

		@Override
		public boolean equals(Object obj) {
				if (this == obj) {
						return true;
				}
				if (!(obj instanceof PatientSymptom)) {
						return false;
				}
				PatientSymptom other = (PatientSymptom) obj;
				return Objects.equals(userId, other.userId)
						&& Objects.equals(dateReported, other.dateReported)  
						&& Objects.equals(dateEnded, other.dateEnded)  
						&& Objects.equals(symptom, other.symptom);
		}

}
