package com.noura.demo.data;

import java.sql.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.Objects;

public class User {

		private Integer userId;
		private	String loginId;
		private	Boolean isActive = false;
		private	String passwd;
		private String firstName;
		private String middleName;
		private String lastName;
		private String title;
		private Date birthDate;
		private Character gender;
		private String address1;
		private String address2;
		private String city;
		private String state;
		private	String email;
		private	String phone;
		private	Date startDate;
		private	Date lastLoginDate;
		private Integer userVersion;
	
		private Role role;
		
		private List<Ethnicity> ethnicities;
		private List<PatientSymptom> symptoms;
		private List<TestResult> testResults;
		private List<PatientToProtocol> patientProtocols;
		
		public User() {
		}

		public User(Integer userId, String loginId, Boolean isActive, String passwd, 
								String firstName, String middleName, String lastName, String title,
								Date birthDate, Character gender, String address1, String address2,
								String city, String state, String email, String phone,
								Date startDate, Date lastLoginDate, Integer userVersion, Role role,
								List<Ethnicity> ethnicities, List<PatientSymptom> symptoms,
								List<TestResult> testResults, List<PatientToProtocol> patientProtocols) {

				super();
				setUserId(userId);
				setLoginId(loginId);
				setIsActive(isActive);
				setPasswd(passwd);
				setFirstName(firstName);
				setMiddleName(middleName);
				setLastName(lastName);
				setTitle(title);
				setBirthDate(birthDate);
				setGender(gender);
				setAddress1(address1);
				setAddress2(address2);
				setCity(city);
				setState(state);
				setEmail(email);
				setPhone(phone);
				setStartDate(startDate);
				setLastLoginDate(lastLoginDate);
				setUserVersion(userVersion);
				setRole(role);
				setEthnicities(ethnicities);
				setSymptoms(symptoms);
				setTestResults(testResults);
				setPatientProtocols(patientProtocols);
		}

		/**
		 * @return the userId
		 */
		public Integer getUserId() {
				return userId;
		}

		/**
		 * @param userId the userId to set, cannot be null
		 */
		void setUserId(Integer userId) {
				if (null == userId) { 
						throw new IllegalArgumentException("The userId input parameter cannot be null."); 
				}
				this.userId = userId;
		}

		/**
		 * @return the loginId
		 */
		public String getLoginId() {
				return loginId;
		}

		/**
		 * @param loginId the loginId to set, cannot be null
		 */
		public void setLoginId(String loginId) {
				if (null == loginId) { 
						throw new IllegalArgumentException("The loginId input parameter cannot be null."); 
				}
				this.loginId = loginId;
		}

		/**
		 * @return the isActive
		 */
		public Boolean getIsActive() {
				return isActive;
		}

		/**
		 * @param isActive the isActive to set, cannot be null
		 */
		public void setIsActive(Boolean isActive) {
				if (null == isActive) { 
						throw new IllegalArgumentException("The isActive input parameter cannot be null."); 
				}
				this.isActive = isActive;
		}

		/**
		 * @return the passwd
		 */
		public String getPasswd() {
				return passwd;
		}

		/**
		 * @param passwd the passwd to set
		 */
		public void setPasswd(String passwd) {
				this.passwd = passwd;
		}

		/**
		 * @return the firstName
		 */
		public String getFirstName() {
				return firstName;
		}

		/**
		 * @param firstName the firstName to set, cannot be null
		 */
		public void setFirstName(String firstName) {
				if (null == firstName) { 
						throw new IllegalArgumentException("The firstName input parameter cannot be null."); 
				}
				this.firstName = firstName;
		}

		/**
		 * @return the middleName
		 */
		public String getMiddleName() {
				return middleName;
		}

		/**
		 * @param middleName the middleName to set
		 */
		public void setMiddleName(String middleName) {
				this.middleName = middleName;
		}

		/**
		 * @return the lastName
		 */
		public String getLastName() {
				return lastName;
		}

		/**
		 * @param lastName the lastName to set, cannot be null
		 */
		public void setLastName(String lastName) {
				if (null == lastName) { 
						throw new IllegalArgumentException("The lastName input parameter cannot be null."); 
				}
				this.lastName = lastName;
		}

		/**
		 * @return the title
		 */
		public String getTitle() {
				return title;
		}

		/**
		 * @param title the title to set
		 */
		public void setTitle(String title) {
				this.title = title;
		}

		/**
		 * @return the birthDate
		 */
		public Date getBirthDate() {
				return birthDate;
		}

		/**
		 * @param birthDate the birthDate to set, cannot be null
		 */
		public void setBirthDate(Date birthDate) {
				if (null == birthDate) { 
						throw new IllegalArgumentException("The birthDate input parameter cannot be null."); 
				}

				Date now = new Date(System.currentTimeMillis());
				if (now.before(birthDate)) {
						throw new IllegalArgumentException("The birthDate input parameter cannot be in the future."); 
				}
		
				this.birthDate = birthDate;
		}

		/**
		 * @return the gender
		 */
		public Character getGender() {
				return gender;
		}

		/**
		 * @param gender the gender to set, cannot be null
		 */
		public void setGender(Character gender) {
				if (null == gender) { 
						throw new IllegalArgumentException("The gender input parameter cannot be null."); 
				}
				this.gender = gender;
		}

		/**
		 * @return the address line 1
		 */
		public String getAddress1() {
				return address1;
		}

		/**
		 * @param address1 the address1 to set, cannot be null
		 */
		public void setAddress1(String address1) {
				if (null == address1) { 
						throw new IllegalArgumentException("The address1 input parameter cannot be null."); 
				}
				this.address1 = address1;
		}

		/**
		 * @return the address line 2
		 */
		public String getAddress2() {
				return address2;
		}

		/**
		 * @param address2 the address2 to set
		 */
		public void setAddress2(String address2) {
				this.address2 = address2;
		}

		/**
		 * @return the city
		 */
		public String getCity() {
				return city;
		}

		/**
		 * @param city the city to set, cannot be null
		 */
		public void setCity(String city) {
				if (null == city) { 
						throw new IllegalArgumentException("The city input parameter cannot be null."); 
				}
				this.city = city;
		}

		/**
		 * @return the state
		 */
		public String getState() {
				return state;
		}

		/**
		 * @param state the state to set, cannot be null
		 */
		public void setState(String state) {
				if (null == state) { 
						throw new IllegalArgumentException("The state input parameter cannot be null."); 
				}
				this.state = state;
		}

		/**
		 * @return the email
		 */
		public String getEmail() {
				return email;
		}

		/**
		 * @param email the email to set, cannot be null
		 */
		public void setEmail(String email) {
				if (null == email) { 
						throw new IllegalArgumentException("The email input parameter cannot be null."); 
				}
				this.email = email;
		}

		/**
		 * @return the phone
		 */
		public String getPhone() {
				return phone;
		}

		/**
		 * @param phone the phone to set, cannot be null
		 */
		public void setPhone(String phone) {
				if (null == phone) { 
						throw new IllegalArgumentException("The phone input parameter cannot be null."); 
				}
				this.phone = phone;
		}

		/**
		 * @return the startDate
		 */
		public Date getStartDate() {
				return startDate;
		}

		/**
		 * @param startDate the startDate to set, cannot be null
		 */
		public void setStartDate(Date startDate) {
				if (null == startDate) { 
						throw new IllegalArgumentException("The startDate input parameter cannot be null."); 
				}
		
				Date now = new Date(System.currentTimeMillis());
				if (now.before(startDate)) {
						throw new IllegalArgumentException("The startDate input parameter cannot be in the future."); 
				}
		
				this.startDate = startDate;
		}

		/**
		 * @return the lastLoginDate
		 */
		public Date getLastLoginDate() {
				return lastLoginDate;
		}

		/**
		 * @param lastLoginDate the lastLoginDate to set, cannot be null
		 */
		public void setLastLoginDate(Date lastLoginDate) {
			
				if (null != lastLoginDate && (lastLoginDate.before(startDate))) { 
						throw new IllegalArgumentException("The lastLoginDate input parameter must be >= the user's startDate."); 
				}

				this.lastLoginDate = lastLoginDate;

		}

		/**
		 * @return the userVersion, used to prevent race conditions on updates
		 */
		public Integer getUserVersion() {
				return userVersion;
		}

		/**
		 * @param userVersion the userVersion to set, cannot be null
		 */
		void setUserVersion(Integer userVersion) {
				if (null == userVersion) { 
						throw new IllegalArgumentException("The userVersion input parameter cannot be null."); 
				}
				this.userVersion = userVersion;
		}

		/**
		 * @return the role
		 */
		public Role getRole() {
				return role;
		}

		/**
		 * @param role the role to set, cannot be null
		 */
		public void setRole(Role role) {
				if (null == role) { 
						throw new IllegalArgumentException("The role input parameter cannot be null."); 
				}
				this.role = role;
		}

		/**
		 * @return the ethnicities
		 */
		public List<Ethnicity> getEthnicities() {
				if (null == this.ethnicities) {
						this.ethnicities = new ArrayList<Ethnicity>();
				}
				return ethnicities;
		}

		/**
		 * @param ethnicities the ethnicities to set
		 */
		public void setEthnicities(List<Ethnicity> ethnicities) {
				if (null == ethnicities) {
						this.ethnicities = new ArrayList<Ethnicity>();
				} else {
						this.ethnicities = ethnicities;
				}
		}

		/**
		 * @return the symptoms
		 */
		public List<PatientSymptom> getSymptoms() {
				if (null == this.symptoms) {
						this.symptoms = new ArrayList<PatientSymptom>();
				}
				return symptoms;
		}

		/**
		 * @param symptoms the symptoms to set
		 */
		public void setSymptoms(List<PatientSymptom> symptoms) {
				if (null == symptoms) {
						this.symptoms = new ArrayList<PatientSymptom>();
				} else {
						this.symptoms = symptoms;
				}
		}

		/**
		 * @return the testResults
		 */
		public List<TestResult> getTestResults() {
				if (null == testResults) {
						this.testResults = new ArrayList<TestResult>();
				}
				return testResults;
		}

		/**
		 * @param testResults the testResults to set
		 */
		public void setTestResults(List<TestResult> testResults) {
				if (null == this.testResults) {
						this.testResults = new ArrayList<TestResult>();
				} else {
						this.testResults = testResults;
				}
		}

		/**
		 * @return the patientProtocols
		 */
		public List<PatientToProtocol> getPatientProtocols() {
				if (null == this.patientProtocols) {
						this.patientProtocols = new ArrayList<PatientToProtocol>();
				}
				return patientProtocols;
		}

		/**
		 * @param patientProtocols the patientProtocols to set
		 */
		public void setPatientProtocols(List<PatientToProtocol> patientProtocols) {
				if (null == patientProtocols) {
						this.patientProtocols = new ArrayList<PatientToProtocol>();
				} else {
						this.patientProtocols = patientProtocols;
				}
		}

		@Override
		public int hashCode() {
				return Objects.hash(userId, loginId, isActive, passwd,
														lastName, firstName, middleName, title,
														birthDate, gender, address1, address2,
														city, state, email, phone,
														startDate, lastLoginDate, role,
														ethnicities, symptoms, patientProtocols,
														testResults);
		}

		@Override
		public boolean equals(Object obj) {

				if (this == obj) {
						return true;
				}
			
				if (!(obj instanceof User)) {
						return false;
				}

				User other = (User) obj;
				return Objects.equals(userId, other.userId)
						&& Objects.equals(loginId, other.loginId)
						&& Objects.equals(isActive, other.isActive)
						&& Objects.equals(lastName, other.lastName)
						&& Objects.equals(firstName, other.firstName)
						&& Objects.equals(middleName, other.middleName)
						&& Objects.equals(title, other.title)
						&& Objects.equals(gender, other.gender) 
						&& Objects.equals(address1, other.address1) 
						&& Objects.equals(address2, other.address2)
						&& Objects.equals(city, other.city) 
						&& Objects.equals(state, other.state) 
						&& Objects.equals(email, other.email) && Objects.equals(phone, other.phone)
						&& Objects.equals(lastLoginDate, other.lastLoginDate) 
						&& Objects.equals(startDate, other.startDate)
						&& Objects.equals(role, other.role)
						&& Objects.equals(ethnicities, other.ethnicities) 
						&& Objects.equals(symptoms, other.symptoms)
						&& Objects.equals(patientProtocols, other.patientProtocols) 
						&& Objects.equals(testResults, other.testResults);
		}

}
