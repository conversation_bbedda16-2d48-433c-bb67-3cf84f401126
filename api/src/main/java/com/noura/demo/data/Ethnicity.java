package com.noura.demo.data;

import java.util.Objects;

public class Ethnicity {

	private Integer ethnicityId;
	private String ethnicityName;
	
	public Ethnicity() {
	}

	public Ethnicity(Integer ethnicityId, String ethnicityName) {
		super();
		setEthnicityId(ethnicityId);
		setEthnicityName(ethnicityName);
	}

	/**
	 * @return the ethnicityId
	 */
	public Integer getEthnicityId() {
		return ethnicityId;
	}

	/**
	 * @param ethnicityId the ethnicityId to set
	 */
	public void setEthnicityId(Integer ethnicityId) {
			if (null == ethnicityId) {
					throw new IllegalArgumentException("The ethnicityId input parameter cannot be null.");
			}
		this.ethnicityId = ethnicityId;
	}

	/**
	 * @return the ethnicityName
	 */
	public String getEthnicityName() {
		return ethnicityName;
	}

	/**
	 * @param ethnicityName the ethnicityName to set
	 */
	public void setEthnicityName(String ethnicityName) {
			if (null == ethnicityName) {
					throw new IllegalArgumentException("The ethnicityName input parameter cannot be null.");
			}
		this.ethnicityName = ethnicityName;
	}

	@Override
	public int hashCode() {
		return Objects.hash(ethnicityId, ethnicityName);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj) {
			return true;
		}
		if (!(obj instanceof Ethnicity)) {
			return false;
		}
		Ethnicity other = (Ethnicity) obj;
		return Objects.equals(ethnicityId, other.ethnicityId) && Objects.equals(ethnicityName, other.ethnicityName);
	}

}
