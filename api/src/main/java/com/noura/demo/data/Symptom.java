package com.noura.demo.data;

import java.util.Objects;

public class Symptom {

		private Integer symptomId;
		private String symptomName;

		public Symptom() {
		}

		public Symptom(Integer symptomId, String symptomName) {
				super();
				setSymptomId(symptomId);
				setSymptomName(symptomName);
	}

		/**
		 * @return the symptomId
		 */
		public Integer getSymptomId() {
				return symptomId;
		}

		/**
		 * @param symptomId the symptomId to set
		 */
		public void setSymptomId(Integer symptomId) {
				if (null == symptomId) {
						throw new IllegalArgumentException("The symptomId input parameter cannot be null.");
				}
				this.symptomId = symptomId;
		}
		
		/**
		 * @return the symptomName
		 */
		public String getSymptomName() {
				return symptomName;
		}
		
		/**
		 * @param symptomName the symptomName to set
		 */
		public void setSymptomName(String symptomName) {
				if (null == symptomName) {
						throw new IllegalArgumentException("The symptomName input parameter cannot be null.");
				}
				this.symptomName = symptomName;
		}
		
		@Override
		public int hashCode() {
				return Objects.hash(symptomId, symptomName);
		}
		
		@Override
		public boolean equals(Object obj) {
				if (this == obj) {
						return true;
				}
				if (!(obj instanceof Symptom)) {
						return false;
				}
				Symptom other = (Symptom) obj;
				return Objects.equals(symptomId, other.symptomId) && Objects.equals(symptomName, other.symptomName);
		}

}
