package com.noura.demo.data;

import java.sql.Date;
import java.util.Objects;

public class TestResult {

	private Integer userId;
	private TestDefinition testDef;
	private Date testDate;
	private Double testValue;
	
	public TestResult() {
	}

	public TestResult(Integer userId, TestDefinition testDef, Date testDate, Double testValue) {
		super();
		setUserId(userId);
		setTestDefinition(testDef);
		setTestDate(testDate);
		setTestValue(testValue);
	}

	/**
	 * @return the userId
	 */
	public Integer getUserId() {
		return userId;
	}

	/**
	 * @param userId the userId to set
	 */
	public void setUserId(Integer userId) {
		if (null == userId) { 
			throw new IllegalArgumentException("The userId input parameter cannot be null."); 
		}
		this.userId = userId;
	}

	/**
	 * @return the testDef
	 */
	public TestDefinition getTestDefinition() {
		return testDef;
	}

	/**
	 * @param testDef the testDef to set
	 */
	public void setTestDefinition(TestDefinition testDef) {
		if (null == testDef) { 
			throw new IllegalArgumentException("The testDef input parameter cannot be null."); 
		}
		this.testDef = testDef;
	}

	/**
	 * @return the testDate
	 */
	public Date getTestDate() {
		return testDate;
	}

	/**
	 * @param testDate the testDate to set
	 */
	public void setTestDate(Date testDate) {
		if (null == testDate) { 
			throw new IllegalArgumentException("The testDate input parameter cannot be null."); 
		}
		this.testDate = testDate;
	}

	/**
	 * @return the testValue
	 */
	public Double getTestValue() {
		return testValue;
	}

	/**
	 * @param testValue the testValue to set
	 */
	public void setTestValue(Double testValue) {
		if (null == testValue) { 
			throw new IllegalArgumentException("The testValue input parameter cannot be null."); 
		}
		this.testValue = testValue;
	}

	@Override
	public int hashCode() {
		return Objects.hash(userId, testDate, testDef, testValue);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj) {
			return true;
		}
		if (!(obj instanceof TestResult)) {
			return false;
		}
		TestResult other = (TestResult) obj;
		return Objects.equals(userId, other.userId) && Objects.equals(testDate, other.testDate)
				&& Objects.equals(testDef, other.testDef) && Objects.equals(testValue, other.testValue);
	}

}
