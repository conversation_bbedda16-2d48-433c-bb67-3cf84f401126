spring:
  application:
    name: api

  # Database Configuration (PostgreSQL)
  datasource:
    url: **************************************
    driver-class-name: org.postgresql.Driver
    username: admin
    password: Good2No!
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: update  # Use 'validate' in production
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
        implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyJpaCompliantImpl
    show-sql: false
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true

  # Security Configuration
  security:
    user:
      name: admin
      password: admin123
      roles: ADMIN

  # Cache Configuration
  cache:
    type: simple
    cache-names: users

  # Jackson Configuration
  jackson:
    property-naming-strategy: LOWER_CAMEL_CASE # convert JSON properties response during serialization
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

  # Async Configuration
  task:
    execution:
      pool:
        core-size: 2
        max-size: 5
        queue-capacity: 100
      thread-name-prefix: api-

# Server Configuration
server:
  port: 8081
  servlet:
    context-path: /
  error:
    include-stacktrace: never
    include-message: always

# Management & Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health, info, metrics, prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
  info:
    env:
      enabled: true

# Application Information
info:
  app:
    name: Noura API
    description: A comprehensive API for managing patients
    version: 1.0.0
    developer: Development Team

# Logging Configuration
logging:
  level:
    com.noura.api: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /api/logs/client-app.log
    max-size: 10MB
    max-history: 10

# Custom Application Properties
app:
  cors:
    allowed-origins: "http://localhost:5173"
  security:
    jwt:
      expiration: 86400000  # 24 hours
  pagination:
    default-page-size: 20
    max-page-size: 100


#Custom Variable

webapp-url: http://localhost:5173
