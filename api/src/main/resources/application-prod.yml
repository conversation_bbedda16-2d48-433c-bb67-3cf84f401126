spring:
#  config:
#    activate:
#      on-profile: prod

  datasource:
    url: ***************************************
    driver-class-name: org.postgresql.Driver
    username: admin
    password: Good2No!
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  jpa:
    hibernate:
      ddl-auto: validate  # Don't modify schema in production
    show-sql: false

server:
  servlet:
   context-path: /api
  port: 2053
  ssl:
    key-store: 'classpath:ssl/keystore.jks'
    key-store-password: secret
    key-store-type: PKCS12
    key-alias: tomcat
  forward-headers-strategy: native

logging:
  level:
    root: INFO
    com.noura.api: INFO

#Custom Variable

webapp-url: https://noura-demo.online
