# Build stage
FROM openjdk:21-jdk-alpine AS builder

WORKDIR /app

# Copy Maven files
COPY pom.xml ./

# Download dependencies (cached layer)
RUN mvn dependency:go-offline -B || true

# Copy source code
COPY src ./src

# Build the application
RUN mvn clean package -DskipTests -B

# Production stage
FROM openjdk:21-jre-alpine

# Create non-root user
RUN addgroup -g 1001 -S spring && \
    adduser -S springuser -u 1001 -G spring

# Install wget for health checks
RUN apk add --no-cache wget

WORKDIR /app

# Copy JAR from builder stage
COPY --from=builder --chown=springuser:spring /app/target/*.jar app.jar

# Create logs directory
RUN mkdir -p /app/logs && chown springuser:spring /app/logs

# Switch to non-root user
USER springuser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:8080/actuator/health || exit 1

# Run the application
CMD ["java", \
     "-XX:+UseContainerSupport", \
     "-XX:MaxRAMPercentage=75.0", \
     "-Djava.security.egd=file:/dev/./urandom", \
     "-jar", "app.jar"]