
INSERT INTO roles (role_id, role_name)
  VALUES (0, 'Admin'), (1,'Patient');

INSERT INTO users (login_id, is_active, passwd,
  first_name, middle_name, last_name, title,
  birth_date, gender, email, phone, address_1,
  address_2, city, state, role_id, start_date,
  last_login_date, user_version)

    VALUES

    ('admin', TRUE, 'admin', 'Admin', NU<PERSON>, 'Administrator',
    'Mr.', '02-22-1932', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 0, '02-22-2025',
    '02-22-2025', 0),

    ('georgew01', TRUE, 'georgew', '<PERSON>', NULL, 'Washington',
    'Mr.', '02-22-1932', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('johna02', TR<PERSON>, 'johna', '<PERSON>', <PERSON><PERSON><PERSON>, '<PERSON>',
    'Mr.', '10-30-1935', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('thomasj03', TRUE, 'thomasj03', 'Thomas', NULL, 'Jefferson',
    'Mr.', '04-13-1926', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('jamesm04', TRUE, 'jamesm04', 'James', NULL, 'Madison',
    'Mr.', '03-16-1951', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('jamesm05', TRUE, 'jamesm05', 'James', NULL, 'Monroe',
    'Mr.', '04-28-1958', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('johnqa06', TRUE, 'jqadams06', 'John', 'Quincy', 'Adams',
    'Mr.', '06-11-1967', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('andrewj07', TRUE, 'andrewj07', 'Andrew', NULL, 'Jackson',
    'Mr.', '03-15-1967', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('martinvb08', TRUE, 'martinvb08', 'Martin', NULL, 'Van Buren',
    'Mr.', '12-05-1962', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('williamhh09', TRUE, 'williamhh09', 'William', 'Henry', 'Harrison',
    'Mr.', '02-09-1941', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('johnt10', TRUE, 'johnt10', 'John', NULL, 'Tyler',
    'Mr.', '03-29-1990', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('jameskp11', TRUE, 'jameskp11', 'James', 'Knox', 'Polk',
    'Mr.', '11-02-1995', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('zacharyt12', TRUE, 'zacharyt12', 'Zachary', NULL, 'Taylor',
    'Mr.', '11-24-1984', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('millardf13', TRUE, 'millardf13', 'Millard', NULL, 'Fillmore',
    'Mr.', '01-07-2000', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('franklinp14', TRUE, 'franklinp14', 'Franklin', NULL, 'Pierce',
    'Mr.', '11-23-2002', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('jamesb15', TRUE, 'jamesb15', 'James', NULL, 'Buchanan',
    'Mr.', '04-23-1991', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('abrahaml16', TRUE, 'abrahaml16', 'Abraham', NULL, 'Lincoln',
    'Mr.', '02-12-1999', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('marthadc01', TRUE, 'marthadc01', 'Martha', 'Dandridge', 'Custis',
    'Mrs.', '06-02-1931', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('abigails02', TRUE, 'abigails02', 'Abigail', NULL, 'Smith',
    'Mrs.', '11-22-1944', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('marthasw03', TRUE, 'marthasw03', 'Martha', 'Skelton', 'Wales',
    'Mrs.', '10-30-1948', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('dolleytp04', TRUE, 'dolleytp04', 'Dolley', 'Todd', 'Payne',
    'Mrs.', '05-20-1968', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('elizabethk05', TRUE, 'elizabethk05', 'Elizabeth', NULL, 'Kortright',
    'Mrs.', '06-30-1968', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('louisacj06', TRUE, 'louisacj06', 'Louisa', 'Catherine', 'Johnson',
    'Mrs.', '02-12-1975', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('racheld07', TRUE, 'racheld07', 'Rachel', NULL, 'Donelson',
    'Mrs.', '06-15-1967', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('hannahh08', TRUE, 'hannahh08', 'Hannah', NULL, 'Hoes',
    'Mrs.', '03-08-1983', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('annats09', TRUE, 'annats09', 'Anna', 'Tuthill', 'Symmes',
    'Mrs.', '07-25-1975', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('letitiac10', TRUE, 'letitiac10', 'Letitia', NULL, 'Christian',
    'Mrs.', '11-12-1990', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('sarahc11', TRUE, 'sarahc11', 'Sarah', NULL, 'Childress',
    'Mrs.', '09-04-2003', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('margarets12', TRUE, 'margarets12', 'Margaret', 'Mackall', 'Smith',
    'Mrs.', '09-21-1988', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('abigailp13', TRUE, 'abigailp13', 'Abigail', NULL, 'Powers',
    'Mrs.', '03-13-1998', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('janea14', TRUE, 'janea14', 'Jane', 'Means', 'Appleton',
    'Mrs.', '03-12-2006', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('harrietj15', TRUE, 'harrietj15', 'Harriet', 'Rebecca Lane', 'Johnston',
    'Mrs.', '05-09-1930', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    ('maryt16', TRUE, 'maryt16', 'Mary', 'Ann', 'Todd',
    'Mrs.', '12-13-1999', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0);
  
INSERT INTO test_groups (test_group_id, test_group_name)

  VALUES

    (0, 'Independent Tests'),
    (1, 'Metabolic Panel'),
    (2, 'Lipid Panel with Reflex to Direct LDL'),
    (3, 'Hemoglobin A1C'),
    (4, 'Complete Blood Count');

INSERT INTO test_definitions (test_id, test_group_id, test_name, test_unit, 
  min_of_range, max_of_range)

  VALUES

    (0, 0, 'Prostate Specific Antigen Screen', 'ng/mL', 0.00, 4.00),
    (1, 1, 'Sodium', 'mmol/L', 136, 145),
    (2, 1, 'Potassium', 'mmol/L', 3.6, 5.1),
    (3, 1, 'Chloride', 'mmol/L', 98, 107),
    (4, 1, 'CO2', 'mmol/L', 22, 32),
    (5, 1, 'Anion Gap', 'Calculated', 6, 18),
    (6, 1, 'Glucose', 'mg/dL', 70, 99),
    (7, 1, 'BUN', 'mg/dL', 8, 20),
    (8, 1, 'Creatinine', 'mg/dL', 0.60, 1.30),
    (9, 1, 'eGFR', 'mL/min/1.73m2', 60, NULL),
    (10, 1, 'BUN/Creatinine Ratio', 'Calculated', 12.0, 20.0),
    (11, 1, 'Calcium', 'mg/dL', 8.9, 10.3),
    (12, 1, 'AST (SGOT)', 'unit/L', 15, 41),
    (13, 1, 'ALT (SGPT)', 'unit/L', 7, 52),
    (14, 1, 'Alkaline Phosphatase', 'unit/L', 32, 91),
    (15, 1, 'Total Protein', 'g/dL', 6.1, 7.9),
    (16, 1, 'Albumin', 'g/dL', 3.5, 4.8),
    (17, 1, 'Total Bilirubin', 'mg/dL', 0.3, 1.2),
    (18, 2, 'Cholesterol', 'mg/dL', NULL, 200),
    (19, 2, 'Triglycerides', 'mg/dL', NULL, 200),
    (20, 2, 'HDL', 'mg/dL', 40, NULL),
    (21, 2, 'LDL Calculated', 'mg/dL', NULL, 100),
    (22, 2, 'VLDL Cholesterol Cal', 'mg/dL', 2, 38),
    (23, 3, 'Hemoglobin A1C', 'percent', NULL, 5.6),
    (24, 3, 'Mean Bld Glu Estim.', 'mg/dL', 0, NULL),
    (25, 4, 'WBC', 'K/mcL', 4.6, 10.2),
    (26, 4, 'RBC', 'M/mcL', 4.3, 5.9),
    (27, 4, 'Hemoglobin', 'g/dL', 13.5, 17.5),
    (28, 4, 'Hematocrit', 'percent', 39.0, 49.0),
    (29, 4, 'MCV', 'FL', 80.0, 97.0),
    (30, 4, 'MCH', 'pcg', 27.0, 34.0),
    (31, 4, 'MCHC', 'g/dL', 30.8, 35.3),
    (32, 4, 'MPV', 'FL', 6.2, 12.1),
    (33, 4, 'Neutrophils Relative', 'percent', 38.1, 75.5),
    (34, 4, 'Lymphocytes Relative', 'percent', 17.9, 49.6),
    (35, 4, 'Monocytes Relative', 'percent', 0.0, 12.0),
    (36, 4, 'Eosinophils Relative', 'percent', 0.0, 7.0),
    (37, 4, 'Basophils Relative', 'percent', 0.0, 2.0),
    (38, 4, 'Immature Granulocytes Relative', 'percent', 0.0, 1.2),
    (39, 4, 'Neutrophils Absolute', 'K/mcL', 1.80, 7.70),
    (40, 4, 'Lymphocytes Absolute', 'K/mcL', 1.00, 4.80),
    (41, 4, 'Monocytes Absolute', 'K/mcL', 0.00, 0.90),
    (42, 4, 'Eosinophils Absolute', 'K/mcL', 0.00, 0.70),
    (43, 4, 'Basophils Absolute', 'K/mcL', 0.00, 0.20),
    (44, 4, 'Immature Granulocytes Absolute', 'K/mcL', 0.00, 0.10),
    (45, 4, 'RDW', 'percent', 11.0, 14.8),
    (46, 4, 'Platelets', 'K/mcL', 142, 424);

  INSERT INTO symptoms (symptom_id, symptom_name)

    VALUES
  
    (0, 'Bloating'),
    (1, 'Body aches'),
    (2, 'Cloudy thinking'),
    (3, 'Cold hands and feet'),
    (4, 'Constipation'),
    (5, 'Diarrhea'),
    (6, 'Fatigue'),
    (7, 'Getting up frequently at night to urinate'),
    (8, 'Hair loss'),
    (9, 'Headaches'),
    (10, 'Heartburn'),
    (11, 'Hives'),
    (12, 'Impotence'),
    (13, 'Mood problems'),
    (14, 'Poor libido'),
    (15, 'Poor sleep'),
    (16, 'Problem complexions'),
    (17, 'Rashes'),
    (18, 'Recurring infections'),
    (19, 'Restless legs'),
    (20, 'Numbness'),
    (21, 'Tingling');

