
-- WARNING!!! -- WARNING!!! -- WARNING!!!

-- DO NOT EVER RUN THIS FILE ON A PRODUCTION DATABASE!!!

-- This file creates an empty database that allows a developer to begin work
-- with a fresh database. If the database and associated tables already exist,
-- THEY WILL BE UNMERCIFULLY, CALLOUSLY, COLD-BLOODEDLY DESTROYED!!!

-- WARNING!!! -- WARNING!!! -- WARNING!!!

DROP DATABASE IF EXISTS demo;
  
CREATE DATABASE demo WITH OWNER = 'noura';

\connect demo

SET role noura;

COMMENT ON DATABASE demo IS 'This is the db for the Noura WEE Protocol application.';

CREATE TABLE roles (
  role_id INTEGER NOT NULL PRIMARY KEY,
  role_name VARCHAR(32) NOT NULL
);

COMMENT ON TABLE roles IS 'This table defines the list of allowed roles.';
  
CREATE TABLE users (
  user_id SERIAL NOT NULL PRIMARY KEY,
  login_id VARCHAR(256) NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT false,
  passwd VARCHAR(60) NOT NULL,
  first_name VARCHAR(255) NOT NULL,
  middle_name VARCHAR(255) NULL,
  last_name VARCHAR(255) NOT NULL,
  title VARCHAR(255) NULL,
  birth_date DATE NOT NULL,
  gender CHAR(1) NOT NULL,
  email VARCHAR(256) NOT NULL,
  phone VARCHAR(256) NOT NULL,
  address_1 VARCHAR(256) NOT NULL,
  address_2 VARCHAR(256) NULL,
  city VARCHAR(256) NOT NULL,
  state CHAR(2) NOT NULL,
  role_id INTEGER NOT NULL,
  start_date DATE NOT NULL,
  last_login_date DATE NULL,
  user_version INTEGER NOT NULL DEFAULT 0,
  CONSTRAINT ck_users_login_id UNIQUE (login_id),
  CONSTRAINT fk_users_roles
    FOREIGN KEY (role_id) REFERENCES roles (role_id),
  CONSTRAINT ck_users_last_login_date
    CHECK (last_login_date IS NULL OR last_login_date >= start_date)
);

CREATE INDEX ix_users_login_id ON users (login_id);
  
COMMENT ON TABLE users IS 'This table has one row for each user, either admin or patient.';

CREATE TABLE ethnicities (
  ethnicity_id INTEGER NOT NULL PRIMARY KEY,
  ethnicity_name VARCHAR(256) NOT NULL
);

COMMENT ON TABLE ethnicities IS 'This table has one row for each potential ethnicity. The ethnicity_name column will use the pgsql ltree for hierarchical definition and access.';

CREATE TABLE user_ethnicities (
  user_id INTEGER NOT NULL,
  ethnicity_id INTEGER NOT NULL,
  CONSTRAINT pk_user_ethnicities
    PRIMARY KEY (user_id, ethnicity_id),
  CONSTRAINT fk_user_ethnicities_user 
    FOREIGN KEY (user_id) REFERENCES users (user_id),
  CONSTRAINT fk_user_ethnicities_ethnicity
    FOREIGN KEY (ethnicity_id) REFERENCES ethnicities (ethnicity_id)
); 

COMMENT ON TABLE user_ethnicities IS 'This table matches users to their list of ethnicities.';

CREATE TABLE test_groups (
  test_group_id INTEGER NOT NULL PRIMARY KEY,
  test_group_name VARCHAR(256)
);

COMMENT ON TABLE test_groups IS 'Tests may provide multiple name/value pairs using test_groups.';
  
CREATE TABLE test_definitions (
  test_id INTEGER NOT NULL PRIMARY KEY,
  test_group_id INTEGER NOT NULL,
  test_name VARCHAR(256) NOT NULL,
  test_unit VARCHAR(32) NOT NULL,
  min_of_range REAL NULL,
  max_of_range REAL NULL,
  CONSTRAINT fk_test_definitions_group
    FOREIGN KEY (test_group_id) REFERENCES test_groups (test_group_id),
  CONSTRAINT ck_test_definitions_range
    CHECK ((min_of_range IS NULL AND max_of_range IS NOT NULL)
      OR (min_of_range IS NOT NULL AND max_of_range IS NULL)
      OR (min_of_range IS NOT NULL AND max_of_range IS NOT NULL))
);

COMMENT ON TABLE test_definitions IS 'This table defines the available tests.';
  
CREATE TABLE test_results (
  user_id INTEGER NOT NULL,
  test_id INTEGER NOT NULL,
  test_date DATE NOT NULL,
  test_value REAL NOT NULL,
  CONSTRAINT pk_test_results
    PRIMARY KEY (user_id, test_id, test_date),
  CONSTRAINT fk_test_results_user 
    FOREIGN KEY (user_id) REFERENCES users (user_id),
  CONSTRAINT fk_test_results_test
    FOREIGN KEY (test_id) REFERENCES test_definitions (test_id)
);

COMMENT ON TABLE test_definitions IS 'This table matches users to their test results.';

CREATE TABLE symptoms (
  symptom_id INTEGER NOT NULL PRIMARY KEY,
  symptom_name VARCHAR(256) NOT NULL
);

COMMENT ON TABLE symptoms IS 'This table defines the list of possible patient symptoms.';

CREATE TABLE user_symptoms (
  user_id INTEGER NOT NULL,
  symptom_id INTEGER NOT NULL,
  date_reported DATE NOT NULL,
  date_ended DATE NULL,
  CONSTRAINT pk_user_symptoms
    PRIMARY KEY (user_id, symptom_id, date_reported),
  CONSTRAINT fk_user_symptoms_user 
    FOREIGN KEY (user_id) REFERENCES users (user_id),
  CONSTRAINT fk_user_symptoms_symptom
    FOREIGN KEY (symptom_id) REFERENCES symptoms (symptom_id)
);

COMMENT ON TABLE user_symptoms IS 'This table matches users to their reported symptoms.';

CREATE TABLE protocols (
  protocol_id INTEGER NOT NULL PRIMARY KEY,
  protocol_name VARCHAR(256) NOT NULL
);

CREATE TABLE symptoms_to_protocols (
  symptom_id INTEGER NOT NULL,
  protocol_id INTEGER NOT NULL,
  CONSTRAINT pk_symptoms_to_protocol
    PRIMARY KEY (symptom_id, protocol_id),
  CONSTRAINT fk_symptoms_to_protocols_symptom 
    FOREIGN KEY (symptom_id) REFERENCES symptoms (symptom_id),
  CONSTRAINT fk_symptoms_to_protocols_protocol
    FOREIGN KEY (protocol_id) REFERENCES protocols (protocol_id)
);

CREATE TABLE ethnicities_to_protocols (
  ethnicity_id INTEGER NOT NULL,
  protocol_id INTEGER NOT NULL,
  CONSTRAINT pk_ethnicities_to_protocol
    PRIMARY KEY (ethnicity_id, protocol_id),
  CONSTRAINT fk_ethnicities_to_protocols_ethnicity 
    FOREIGN KEY (ethnicity_id) REFERENCES ethnicities (ethnicity_id),
  CONSTRAINT fk_ethnicities_to_protocols_protocol
    FOREIGN KEY (protocol_id) REFERENCES protocols (protocol_id)
);

CREATE TABLE tests_to_protocols (
  test_id INTEGER NOT NULL,
  protocol_id INTEGER NOT NULL,
  CONSTRAINT pk_tests_to_protocol
    PRIMARY KEY (test_id, protocol_id),
  CONSTRAINT fk_tests_to_protocols_test 
    FOREIGN KEY (test_id) REFERENCES test_definitions (test_id),
  CONSTRAINT fk_tests_to_protocols_protocol
    FOREIGN KEY (protocol_id) REFERENCES protocols (protocol_id)
);

CREATE TABLE users_to_protocols (
  user_id INTEGER NOT NULL,
  protocol_id INTEGER NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  CONSTRAINT pk_user_to_protocol
    PRIMARY KEY (user_id, protocol_id, start_date),
  CONSTRAINT ck_user_to_protocol_date
    CHECK (end_date IS NULL OR end_date >= start_date)
);

