select * from ethnicities;

select * from users;

select * from patients;

select count(*) from ethnicities;
select count(*) from ethnicities_to_protocol;
select count(*) from patient_ethnicities;
select count(*) from patient_symptoms;
select count(*) from patients;
select count(*) from protocols;
select count(*) from symptoms;
select count(*) from symptoms_to_protocol;
select count(*) from test_definitions;
select count(*) from test_results;
select count(*) from tests_to_protocol;
select count(*) from users;

delete from ethnicities;
delete from ethnicities_to_protocol;
delete from patient_ethnicities;
delete from patient_symptoms;
delete from patients;
delete from protocols;
delete from symptoms;
delete from symptoms_to_protocol;
delete from test_definitions;
delete from test_results;
delete from tests_to_protocol;
delete from users;

