import React, {useEffect} from "react";
import {
  splitContainer,
  brandSide,
  brandGradient,
  glassOrb,
  orb1,
  orb2,
  orb3,
  drift,
  glassPanels,
  glassPanel,
  panel1,
  panel2,
  panelFloat,
  brandLogo,
  logoSquare,
  formSide,
  formBackdrop,
  formContainer,
  formLogo,
  formLogoSquare,
  btnPrimary,
  btnSecondary,
  dividerLine,
  dividerSpan,
} from "./Auth.css";
import {useQuery} from "@tanstack/react-query";
import {getUsers, type User} from "../../../../services/userService.ts";

const Auth: React.FC = () => {
  //TODO: Remove, its just for testing purpose.
  const { data } = useQuery<User[]>({
    queryKey: ["users"],
    queryFn: getUsers,
  });

  useEffect(() => {
    if (data) {
      console.log("Users:", data);
    }
  }, [data]);

  return (
    <div className={`${splitContainer} flex h-screen overflow-hidden`}>
      {/* Brand Side */}
      <div
        className={`${brandSide} ${brandGradient} flex-1 flex items-center justify-center relative overflow-hidden`}
      >
        {/* Glass Elements */}
        <div className="absolute inset-0">
          <div
            className={`${glassOrb} ${orb1} ${drift}`}
            style={{ animationDelay: "0s" }}
          />
          <div
            className={`${glassOrb} ${orb2} ${drift}`}
            style={{ animationDelay: "5s" }}
          />
          <div
            className={`${glassOrb} ${orb3} ${drift}`}
            style={{ animationDelay: "10s" }}
          />

          <div
            className={`${glassPanels} absolute top-1/5 right-[10%] w-[120px] h-[200px]`}
          >
            <div className={`${glassPanel} ${panel1} ${panelFloat}`} />
            <div
              className={`${glassPanel} ${panel2} ${panelFloat}`}
              style={{ animationDirection: "reverse" }}
            />
          </div>
        </div>

        {/* Brand Content */}
        <div className="text-center text-white z-10 relative max-w-[380px]">
          <div
            className={`${brandLogo} w-[75px] h-[75px] mx-auto mb-7 flex items-center justify-center rounded-[20px] border border-white/20 shadow-lg`}
          >
            <div className="grid grid-cols-2 gap-[5px]">
              <div className={logoSquare} />
              <div className={logoSquare} />
              <div className={logoSquare} />
              <div className={logoSquare} />
            </div>
          </div>
          <h1 className="text-[30px] font-semibold mb-[14px] tracking-[-0.4px] text-shadow-md">
            Noura Health
          </h1>
          <p className="text-[15px] opacity-95 leading-relaxed">
            Empowering Lives Through Better Health and Trusted Care, Supporting
            You Every Step Toward a Healthier Future.
          </p>
        </div>
      </div>

      {/* Form Side */}
      <div
        className={`${formSide} flex-1 flex items-center justify-center py-[50px] px-10 relative`}
      >
        <div className={formBackdrop} />

        <div
          className={`${formContainer} w-full max-w-[400px] py-[45px] px-10 rounded-2xl border border-white/30 relative z-10`}
        >
          {/* Form Header */}
          <div className="text-center mb-[35px]">
            <div
              className={`${formLogo} w-[52px] h-[52px] mx-auto mb-[22px] flex items-center justify-center rounded-[14px] border border-black/8`}
            >
              <div className="grid grid-cols-2 gap-1">
                <div className={formLogoSquare} />
                <div className={formLogoSquare} />
                <div className={formLogoSquare} />
                <div className={formLogoSquare} />
              </div>
            </div>
            <h2 className="text-gray-900 text-2xl font-semibold mb-2">
              Welcome back
            </h2>
            <p className="text-gray-500 text-sm">Sign in to your account</p>
          </div>

          {/* Buttons */}
          <button
            className={`${btnPrimary} w-full py-4 px-[22px] my-[10px] rounded-[10px] text-[15px] font-medium cursor-pointer transition-all duration-300 border`}
          >
            Sign In
          </button>

          <div className="my-6 text-gray-500 text-[13px] relative text-center">
            <div className={dividerLine} />
            <span className={dividerSpan}>or</span>
          </div>

          <button
            className={`${btnSecondary} w-full py-4 px-[22px] my-[10px] rounded-[10px] text-[15px] font-medium cursor-pointer transition-all duration-300 border`}
          >
            Create Account
          </button>

          {/* Footer */}
          <div className="mt-6 text-xs text-gray-400 text-center">
            Secured with{" "}
            <a href="#" className="text-indigo-500 hover:underline">
              enterprise security
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Auth;
