import { style, keyframes } from "@vanilla-extract/css";

// Keyframes
export const drift = keyframes({
  "0%, 100%": {
    transform: "translateX(0px) translateY(0px)",
  },
  "25%": {
    transform: "translateX(20px) translateY(-15px)",
  },
  "50%": {
    transform: "translateX(-10px) translateY(-30px)",
  },
  "75%": {
    transform: "translateX(15px) translateY(-10px)",
  },
});

export const panelFloat = keyframes({
  "0%, 100%": { transform: "translateY(0px)" },
  "50%": { transform: "translateY(-15px)" },
});

// Base styles
export const splitContainer = style({
  fontFamily:
    '-apple-system, BlinkMacSystemFont, "Segoe UI", system-ui, sans-serif',
});

export const brandSide = style({});

export const brandGradient = style({
  background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
});

export const glassOrb = style({
  position: "absolute",
  background: "rgba(255, 255, 255, 0.1)",
  backdropFilter: "blur(30px)",
  border: "1px solid rgba(255, 255, 255, 0.15)",
  borderRadius: "50%",
  animation: `${drift} 15s ease-in-out infinite`,
});

export const orb1 = style({
  width: "180px",
  height: "180px",
  top: "10%",
  left: "-60px",
});

export const orb2 = style({
  width: "120px",
  height: "120px",
  bottom: "15%",
  right: "-40px",
});

export const orb3 = style({
  width: "80px",
  height: "80px",
  top: "55%",
  left: "15%",
});

export const glassPanels = style({});

export const glassPanel = style({
  position: "absolute",
  background: "rgba(255, 255, 255, 0.08)",
  backdropFilter: "blur(25px)",
  border: "1px solid rgba(255, 255, 255, 0.12)",
  borderRadius: "15px",
});

export const panel1 = style({
  width: "60px",
  height: "120px",
  top: 0,
  left: 0,
  animation: `${panelFloat} 8s ease-in-out infinite`,
});

export const panel2 = style({
  width: "40px",
  height: "80px",
  top: "40px",
  right: 0,
  animation: `${panelFloat} 10s ease-in-out infinite`,
});

export const brandLogo = style({
  background: "rgba(255, 255, 255, 0.1)",
  backdropFilter: "blur(40px)",
  boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
});

export const logoSquare = style({
  width: "11px",
  height: "11px",
  background: "rgba(255, 255, 255, 0.9)",
  borderRadius: "3px",
  backdropFilter: "blur(10px)",
});

export const formSide = style({
  background: "rgba(248, 250, 252, 0.95)",
  backdropFilter: "blur(20px)",
});

export const formBackdrop = style({
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  background: `
    radial-gradient(circle at 30% 20%, rgba(102, 126, 234, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(118, 75, 162, 0.03) 0%, transparent 50%)
  `,
});

export const formContainer = style({
  background: "rgba(255, 255, 255, 0.8)",
  backdropFilter: "blur(25px)",
  boxShadow: `
    0 8px 32px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.5)
  `,
});

export const formLogo = style({
  background: "rgba(248, 250, 252, 0.8)",
  backdropFilter: "blur(15px)",
});

export const formLogoSquare = style({
  width: "8px",
  height: "8px",
  background: "#495057",
  borderRadius: "2px",
});

export const btnPrimary = style({
  background: "rgba(102, 126, 234, 0.9)",
  color: "white",
  borderColor: "rgba(102, 126, 234, 0.3)",
  boxShadow: "0 4px 20px rgba(102, 126, 234, 0.2)",
  backdropFilter: "blur(10px)",
  ":hover": {
    transform: "translateY(-2px)",
    background: "rgba(90, 111, 216, 0.95)",
    boxShadow: "0 8px 30px rgba(102, 126, 234, 0.35)",
  },
});

export const btnSecondary = style({
  background: "rgba(255, 255, 255, 0.6)",
  color: "#4a5568",
  borderColor: "rgba(0, 0, 0, 0.1)",
  backdropFilter: "blur(10px)",
  ":hover": {
    transform: "translateY(-2px)",
    background: "rgba(255, 255, 255, 0.8)",
    borderColor: "rgba(0, 0, 0, 0.15)",
    boxShadow: "0 6px 20px rgba(0, 0, 0, 0.1)",
  },
});

export const dividerLine = style({
  position: "absolute",
  top: "50%",
  left: 0,
  right: 0,
  height: "1px",
  background: "rgba(0, 0, 0, 0.1)",
  content: '""',
});

export const dividerSpan = style({
  background: "rgba(255, 255, 255, 0.8)",
  backdropFilter: "blur(10px)",
  padding: "0 16px",
  position: "relative",
});

// Media queries
export const mobileStyles = style({
  "@media": {
    "(max-width: 768px)": {
      flexDirection: "column",
    },
  },
});
