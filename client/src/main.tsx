import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
//import {AuthPage} from "./components/Auth/AuthPage.tsx";
import Auth from "./features/auth/components/Auth/Auth.tsx";
import {QueryClient, QueryClientProvider} from "@tanstack/react-query";

const queryClient = new QueryClient();

createRoot(document.getElementById("root")!).render(
  <StrictMode>
      <QueryClientProvider client={queryClient}>
          <Auth />
      </QueryClientProvider>
  </StrictMode>,
);
